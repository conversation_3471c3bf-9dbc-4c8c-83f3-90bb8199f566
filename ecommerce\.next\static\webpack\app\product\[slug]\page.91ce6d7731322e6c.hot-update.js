"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductDescription.tsx":
/*!***************************************************!*\
  !*** ./components/product/ProductDescription.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst ProductDescription = (param)=>{\n    let { description, productName = '', productPrice, productBrand } = param;\n    _s();\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        0,\n        1\n    ])); // Expand first two sections by default\n    const toggleSection = (index)=>{\n        const newExpanded = new Set(expandedSections);\n        if (newExpanded.has(index)) {\n            newExpanded.delete(index);\n        } else {\n            newExpanded.add(index);\n        }\n        setExpandedSections(newExpanded);\n    };\n    const parseSpecifications = (items)=>{\n        const specs = [];\n        items.forEach((item)=>{\n            // Try to parse key-value pairs (e.g., \"Power: 3 W\", \"Model: HW80\")\n            const colonIndex = item.indexOf(':');\n            if (colonIndex > 0 && colonIndex < item.length - 1) {\n                specs.push({\n                    key: item.substring(0, colonIndex).trim(),\n                    value: item.substring(colonIndex + 1).trim()\n                });\n                return;\n            }\n            // Try to extract power specifications (e.g., \"3 W\", \"1500 W\")\n            const powerMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*W/i);\n            if (powerMatch) {\n                specs.push({\n                    key: 'Power Consumption',\n                    value: powerMatch[0]\n                });\n                return;\n            }\n            // Try to extract model numbers (e.g., \"Model HW80\", \"HW80\")\n            const modelMatch = item.match(/(?:Model\\s+)?([A-Z]+\\d+[A-Z]*\\d*)/i);\n            if (modelMatch) {\n                specs.push({\n                    key: 'Model Number',\n                    value: modelMatch[1]\n                });\n                return;\n            }\n            // Try to extract capacity (e.g., \"8 kg\", \"10 L\")\n            const capacityMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|l|liters?|litres?)/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: \"\".concat(capacityMatch[1], \" \").concat(capacityMatch[2].toLowerCase())\n                });\n                return;\n            }\n            // Try to extract RPM (e.g., \"1400 RPM\")\n            const rpmMatch = item.match(/(\\d+)\\s*rpm/i);\n            if (rpmMatch) {\n                specs.push({\n                    key: 'Spin Speed',\n                    value: \"\".concat(rpmMatch[1], \" RPM\")\n                });\n                return;\n            }\n            // Try to extract dimensions\n            const dimensionMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*(cm|mm|m)/i);\n            if (dimensionMatch) {\n                specs.push({\n                    key: 'Dimensions',\n                    value: \"\".concat(dimensionMatch[1], \" x \").concat(dimensionMatch[2], \" x \").concat(dimensionMatch[3], \" \").concat(dimensionMatch[4])\n                });\n                return;\n            }\n            // Try to extract weight\n            const weightMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|g)/i);\n            if (weightMatch && !item.toLowerCase().includes('capacity')) {\n                specs.push({\n                    key: 'Weight',\n                    value: \"\".concat(weightMatch[1], \" \").concat(weightMatch[2])\n                });\n                return;\n            }\n            // If no specific pattern matches, treat as a general specification\n            if (item.trim()) {\n                specs.push({\n                    key: 'Feature',\n                    value: item.trim()\n                });\n            }\n        });\n        return specs;\n    };\n    const isGenericContent = (text)=>{\n        const genericPhrases = [\n            'high quality',\n            'reliable performance',\n            'durable construction',\n            'professional-grade performance and reliability',\n            'designed for optimal performance',\n            'excellent features and performance',\n            'quality product designed for reliable performance',\n            'high-quality construction ensures durability',\n            'reliable performance for consistent results',\n            'user-friendly design for easy operation',\n            'premium quality features',\n            'excellent value for money',\n            'designed for long-lasting performance'\n        ];\n        const lowerText = text.toLowerCase();\n        return genericPhrases.some((phrase)=>lowerText.includes(phrase));\n    };\n    const filterGenericItems = (items)=>{\n        return items.filter((item)=>!isGenericContent(item));\n    };\n    const parseDescription = (desc)=>{\n        if (!desc || !desc.trim()) return null;\n        // Clean up the description and handle the actual format we receive\n        const cleanDesc = desc.trim();\n        // Extract overview (everything before the first section)\n        const overviewMatch = cleanDesc.match(/^(.*?)(?=\\s*(?:Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\s*•)/);\n        let overview = overviewMatch ? overviewMatch[1].trim() : '';\n        // Filter out generic overview content\n        if (isGenericContent(overview)) {\n            overview = ''; // Hide generic overviews\n        }\n        // Find all sections using regex that matches our actual format\n        const sectionRegex = /(Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\s*((?:•[^•]*)*)/g;\n        const sections = [];\n        let match;\n        while((match = sectionRegex.exec(cleanDesc)) !== null){\n            const title = match[1].trim();\n            const content = match[2].trim();\n            // Extract bullet points\n            const bulletRegex = /•\\s*([^•]+)/g;\n            const items = [];\n            let bulletMatch;\n            while((bulletMatch = bulletRegex.exec(content)) !== null){\n                const item = bulletMatch[1].trim();\n                if (item) {\n                    items.push(item);\n                }\n            }\n            if (items.length > 0) {\n                // Filter out generic items\n                const filteredItems = filterGenericItems(items);\n                // Only include section if it has non-generic content\n                if (filteredItems.length > 0) {\n                    // Determine section type\n                    let sectionType = 'list';\n                    if (title.toLowerCase().includes('specification')) {\n                        sectionType = 'specifications';\n                    } else if (title.toLowerCase().includes('feature')) {\n                        sectionType = 'features';\n                    }\n                    sections.push({\n                        title,\n                        items: filteredItems,\n                        type: sectionType\n                    });\n                }\n            }\n        }\n        return {\n            overview,\n            sections\n        };\n    };\n    const getSectionIcon = (title)=>{\n        const iconClass = \"w-5 h-5\";\n        switch(title.toLowerCase()){\n            case 'key features':\n            case 'features':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-amber-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, undefined);\n            case 'technical specifications':\n            case 'specifications':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-blue-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 16\n                }, undefined);\n            case 'benefits':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-green-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 16\n                }, undefined);\n            case 'ideal for':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-purple-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-gray-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const renderSpecificationTable = (items)=>{\n        const specs = parseSpecifications(items);\n        if (specs.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic text-center py-4\",\n                children: \"No specifications available\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden border border-gray-200 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-3 sm:px-4 lg:px-6 py-2 sm:py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-sm sm:text-base\",\n                        children: \"Technical Specifications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"divide-y divide-gray-200\",\n                    children: specs.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row hover:bg-blue-50 transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-2/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-gray-50 sm:border-r border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-semibold text-gray-800 block\",\n                                        children: spec.key\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-3/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-gray-900 font-medium block\",\n                                        children: spec.value || '—'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 px-3 sm:px-4 lg:px-6 py-1 sm:py-2 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 text-center\",\n                        children: \"Specifications may vary by model and region\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderFeatureGrid = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Key Features & Highlights\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-3 lg:gap-4\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200 hover:from-amber-100 hover:to-orange-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1 sm:mt-1.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderBenefitsList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Benefits & Advantages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 sm:space-y-3\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 hover:from-green-100 hover:to-emerald-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-green-600 mt-0.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderDefaultList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start p-2 hover:bg-gray-50 rounded-md transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-700\",\n                            children: item\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 328,\n            columnNumber: 7\n        }, undefined);\n    };\n    const parsedDesc = parseDescription(description);\n    // If no meaningful content found, don't show anything\n    if (!parsedDesc || parsedDesc.sections.length === 0 && !parsedDesc.overview) {\n        // Only show raw description if it exists and is not generic\n        if (description && description.trim() && !isGenericContent(description)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose prose-sm max-w-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 border border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, undefined);\n        }\n        // No meaningful description available - don't render anything\n        return null;\n    }\n    const finalDesc = parsedDesc;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-description space-y-4 sm:space-y-6\",\n        children: [\n            finalDesc.overview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overview-section bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 sm:p-6 border border-blue-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 mt-1 mr-2 sm:mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-base sm:text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"Product Overview\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed text-sm sm:text-base\",\n                                    children: finalDesc.overview\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 366,\n                columnNumber: 9\n            }, undefined),\n            finalDesc.sections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"description-section bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>toggleSection(index),\n                            className: \"w-full px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center min-w-0 flex-1\",\n                                    children: [\n                                        getSectionIcon(section.title),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 sm:ml-3 text-sm sm:text-lg font-semibold text-gray-900 truncate\",\n                                            children: section.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500 flex-shrink-0\",\n                                            children: [\n                                                \"(\",\n                                                section.items.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, undefined),\n                                expandedSections.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, undefined),\n                        expandedSections.has(index) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6\",\n                            children: [\n                                section.type === 'specifications' && renderSpecificationTable(section.items),\n                                section.type === 'features' && renderFeatureGrid(section.items),\n                                section.title.toLowerCase().includes('benefit') && renderBenefitsList(section.items),\n                                section.type === 'list' && !section.title.toLowerCase().includes('benefit') && renderDefaultList(section.items)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, undefined))\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n        lineNumber: 363,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductDescription, \"b/WJgSHj1mMZYb0GaMgxOeMj6ek=\");\n_c = ProductDescription;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDescription);\nvar _c;\n$RefreshReg$(_c, \"ProductDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductDescription.tsx\n"));

/***/ })

});