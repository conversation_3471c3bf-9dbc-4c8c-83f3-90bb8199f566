"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductDescription.tsx":
/*!***************************************************!*\
  !*** ./components/product/ProductDescription.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst ProductDescription = (param)=>{\n    let { description, productName = '', productPrice, productBrand } = param;\n    _s();\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        0,\n        1\n    ])); // Expand first two sections by default\n    const toggleSection = (index)=>{\n        const newExpanded = new Set(expandedSections);\n        if (newExpanded.has(index)) {\n            newExpanded.delete(index);\n        } else {\n            newExpanded.add(index);\n        }\n        setExpandedSections(newExpanded);\n    };\n    const parseSpecifications = (items)=>{\n        const specs = [];\n        items.forEach((item)=>{\n            // Try to parse key-value pairs (e.g., \"Power: 3 W\", \"Model: HW80\")\n            const colonIndex = item.indexOf(':');\n            if (colonIndex > 0 && colonIndex < item.length - 1) {\n                specs.push({\n                    key: item.substring(0, colonIndex).trim(),\n                    value: item.substring(colonIndex + 1).trim()\n                });\n                return;\n            }\n            // Try to extract power specifications (e.g., \"3 W\", \"1500 W\")\n            const powerMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*W/i);\n            if (powerMatch) {\n                specs.push({\n                    key: 'Power Consumption',\n                    value: powerMatch[0]\n                });\n                return;\n            }\n            // Try to extract model numbers (e.g., \"Model HW80\", \"HW80\")\n            const modelMatch = item.match(/(?:Model\\s+)?([A-Z]+\\d+[A-Z]*\\d*)/i);\n            if (modelMatch) {\n                specs.push({\n                    key: 'Model Number',\n                    value: modelMatch[1]\n                });\n                return;\n            }\n            // Try to extract capacity (e.g., \"8 kg\", \"10 L\")\n            const capacityMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|l|liters?|litres?)/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: \"\".concat(capacityMatch[1], \" \").concat(capacityMatch[2].toLowerCase())\n                });\n                return;\n            }\n            // Try to extract RPM (e.g., \"1400 RPM\")\n            const rpmMatch = item.match(/(\\d+)\\s*rpm/i);\n            if (rpmMatch) {\n                specs.push({\n                    key: 'Spin Speed',\n                    value: \"\".concat(rpmMatch[1], \" RPM\")\n                });\n                return;\n            }\n            // Try to extract dimensions\n            const dimensionMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*(cm|mm|m)/i);\n            if (dimensionMatch) {\n                specs.push({\n                    key: 'Dimensions',\n                    value: \"\".concat(dimensionMatch[1], \" x \").concat(dimensionMatch[2], \" x \").concat(dimensionMatch[3], \" \").concat(dimensionMatch[4])\n                });\n                return;\n            }\n            // Try to extract weight\n            const weightMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|g)/i);\n            if (weightMatch && !item.toLowerCase().includes('capacity')) {\n                specs.push({\n                    key: 'Weight',\n                    value: \"\".concat(weightMatch[1], \" \").concat(weightMatch[2])\n                });\n                return;\n            }\n            // If no specific pattern matches, treat as a general specification\n            if (item.trim()) {\n                specs.push({\n                    key: 'Feature',\n                    value: item.trim()\n                });\n            }\n        });\n        return specs;\n    };\n    const isGenericContent = (text)=>{\n        const genericPhrases = [\n            'high quality',\n            'reliable performance',\n            'durable construction',\n            'professional-grade performance and reliability',\n            'designed for optimal performance',\n            'excellent features and performance'\n        ];\n        const lowerText = text.toLowerCase();\n        return genericPhrases.some((phrase)=>lowerText.includes(phrase));\n    };\n    const filterGenericItems = (items)=>{\n        return items.filter((item)=>!isGenericContent(item));\n    };\n    const extractSpecsFromProductName = (productName)=>{\n        const specs = [];\n        const name = productName.toLowerCase();\n        // Extract model number\n        const modelMatch = productName.match(/([A-Z]+\\d+[-\\w]*)/);\n        if (modelMatch) {\n            specs.push({\n                key: 'Model',\n                value: modelMatch[1]\n            });\n        }\n        // Extract dimensions\n        const dimensionMatch = productName.match(/(\\d+(?:\\.\\d+)?)\\s*(?:cm|mm|inch)/i);\n        if (dimensionMatch) {\n            specs.push({\n                key: 'Size',\n                value: dimensionMatch[0]\n            });\n        }\n        // Extract capacity for washing machines\n        if (name.includes('washing') || name.includes('washer')) {\n            const capacityMatch = productName.match(/(\\d+(?:\\.\\d+)?)\\s*kg/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: capacityMatch[0]\n                });\n            }\n        }\n        // Extract power ratings\n        const powerMatch = productName.match(/(\\d+)\\s*w/i);\n        if (powerMatch) {\n            specs.push({\n                key: 'Power',\n                value: \"\".concat(powerMatch[1], \" W\")\n            });\n        }\n        return specs;\n    };\n    const parseDescription = (desc)=>{\n        if (!desc || !desc.trim()) return null;\n        // Clean up the description and handle the actual format we receive\n        const cleanDesc = desc.trim();\n        // Extract overview (everything before the first section)\n        const overviewMatch = cleanDesc.match(RegExp(\"^(.*?)(?=\\\\s*(?:Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\\\s*•)\", \"s\"));\n        let overview = overviewMatch ? overviewMatch[1].trim() : '';\n        // Filter out generic overview content\n        if (isGenericContent(overview)) {\n            overview = ''; // Hide generic overviews\n        }\n        // Find all sections using regex that matches our actual format\n        const sectionRegex = /(Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\s*((?:•[^•]*)*)/g;\n        const sections = [];\n        let match;\n        while((match = sectionRegex.exec(cleanDesc)) !== null){\n            const title = match[1].trim();\n            const content = match[2].trim();\n            // Extract bullet points\n            const bulletRegex = /•\\s*([^•]+)/g;\n            const items = [];\n            let bulletMatch;\n            while((bulletMatch = bulletRegex.exec(content)) !== null){\n                const item = bulletMatch[1].trim();\n                if (item) {\n                    items.push(item);\n                }\n            }\n            if (items.length > 0) {\n                // Filter out generic items\n                const filteredItems = filterGenericItems(items);\n                // Only include section if it has non-generic content\n                if (filteredItems.length > 0) {\n                    // Determine section type\n                    let sectionType = 'list';\n                    if (title.toLowerCase().includes('specification')) {\n                        sectionType = 'specifications';\n                    } else if (title.toLowerCase().includes('feature')) {\n                        sectionType = 'features';\n                    }\n                    sections.push({\n                        title,\n                        items: filteredItems,\n                        type: sectionType\n                    });\n                }\n            }\n        }\n        return {\n            overview,\n            sections\n        };\n    };\n    const getSectionIcon = (title)=>{\n        const iconClass = \"w-5 h-5\";\n        switch(title.toLowerCase()){\n            case 'key features':\n            case 'features':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-amber-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 16\n                }, undefined);\n            case 'technical specifications':\n            case 'specifications':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-blue-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 16\n                }, undefined);\n            case 'benefits':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-green-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 16\n                }, undefined);\n            case 'ideal for':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-purple-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-gray-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const renderSpecificationTable = (items)=>{\n        const specs = parseSpecifications(items);\n        if (specs.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic text-center py-4\",\n                children: \"No specifications available\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden border border-gray-200 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-3 sm:px-4 lg:px-6 py-2 sm:py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-sm sm:text-base\",\n                        children: \"Technical Specifications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"divide-y divide-gray-200\",\n                    children: specs.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row hover:bg-blue-50 transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-2/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-gray-50 sm:border-r border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-semibold text-gray-800 block\",\n                                        children: spec.key\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-3/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-gray-900 font-medium block\",\n                                        children: spec.value || '—'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 px-3 sm:px-4 lg:px-6 py-1 sm:py-2 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 text-center\",\n                        children: \"Specifications may vary by model and region\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderFeatureGrid = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Key Features & Highlights\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-3 lg:gap-4\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200 hover:from-amber-100 hover:to-orange-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1 sm:mt-1.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 310,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderBenefitsList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Benefits & Advantages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 sm:space-y-3\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 hover:from-green-100 hover:to-emerald-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-green-600 mt-0.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderDefaultList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start p-2 hover:bg-gray-50 rounded-md transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-700\",\n                            children: item\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, undefined);\n    };\n    const generateFallbackContent = ()=>{\n        const specs = extractSpecsFromProductName(productName);\n        // Add price if available\n        if (productPrice) {\n            specs.push({\n                key: 'Price',\n                value: \"₹\".concat(productPrice.toLocaleString())\n            });\n        }\n        // Add brand if available\n        if (productBrand) {\n            specs.push({\n                key: 'Brand',\n                value: productBrand\n            });\n        }\n        return {\n            overview: \"\".concat(productName, \" is a quality product designed for reliable performance.\"),\n            sections: specs.length > 0 ? [\n                {\n                    title: 'Specifications',\n                    items: specs.map((spec)=>\"\".concat(spec.key, \": \").concat(spec.value)),\n                    type: 'specifications'\n                }\n            ] : []\n        };\n    };\n    const parsedDesc = parseDescription(description);\n    // If no meaningful content found, generate fallback\n    const finalDesc = parsedDesc && parsedDesc.sections.length > 0 ? parsedDesc : generateFallbackContent();\n    if (!finalDesc || finalDesc.sections.length === 0 && !finalDesc.overview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"prose prose-sm max-w-none\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: description || \"\".concat(productName, \" - Product information will be updated soon.\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 395,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-description space-y-4 sm:space-y-6\",\n        children: [\n            parsedDesc.overview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overview-section bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 sm:p-6 border border-blue-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 mt-1 mr-2 sm:mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-base sm:text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"Product Overview\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed text-sm sm:text-base\",\n                                    children: parsedDesc.overview\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 409,\n                columnNumber: 9\n            }, undefined),\n            parsedDesc.sections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"description-section bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>toggleSection(index),\n                            className: \"w-full px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center min-w-0 flex-1\",\n                                    children: [\n                                        getSectionIcon(section.title),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 sm:ml-3 text-sm sm:text-lg font-semibold text-gray-900 truncate\",\n                                            children: section.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500 flex-shrink-0\",\n                                            children: [\n                                                \"(\",\n                                                section.items.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, undefined),\n                                expandedSections.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, undefined),\n                        expandedSections.has(index) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6\",\n                            children: [\n                                section.type === 'specifications' && renderSpecificationTable(section.items),\n                                section.type === 'features' && renderFeatureGrid(section.items),\n                                section.title.toLowerCase().includes('benefit') && renderBenefitsList(section.items),\n                                section.type === 'list' && !section.title.toLowerCase().includes('benefit') && renderDefaultList(section.items)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, undefined))\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n        lineNumber: 406,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductDescription, \"b/WJgSHj1mMZYb0GaMgxOeMj6ek=\");\n_c = ProductDescription;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDescription);\nvar _c;\n$RefreshReg$(_c, \"ProductDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductDescription.tsx\n"));

/***/ })

});