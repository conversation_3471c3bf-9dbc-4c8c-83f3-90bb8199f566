import { Star } from "lucide-react";
import { Button } from "../../components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import { useToast } from "../../components/ui/use-toast";
import useApi from "../../hooks/useApi";
import { ADD_TO_CART, MAIN_URL } from "../../constant/urls";
import { usePathname, useRouter } from "next/navigation";
import { Toaster } from "../ui/toaster";
import { useSession } from "next-auth/react";
import ProductDescription from "./ProductDescription";

interface ProductInfoProps {
  product: {
    id: number;
    name: string;
    price: number;
    description: string;
    average_rating: number;
    reviews: number;
    colors: string[];
    sizes: string[];
    brand?: {
      id: number;
      name: string;
      image?: string;
      image_url?: string;
    };
  };
  selectedColor: string;
  selectedSize: string;
  quantity: number;
  onColorChange: (color: string) => void;
  onSizeChange: (size: string) => void;
  onQuantityChange: (quantity: number) => void;
}

export const ProductInfo = ({
  product,
  selectedColor,
  selectedSize,
  quantity,
  onColorChange,
  onSizeChange,
  onQuantityChange,
}: ProductInfoProps) => {
  const { toast } = useToast();
  const { create } = useApi(MAIN_URL);
  const router = useRouter();
  const pathName = usePathname();
  const { status } = useSession();

  const handleAddToCart = async () => {
    try {
      const res: any = await create(ADD_TO_CART, {
        product_id: product.id,
        quantity: 1,
      });
      if (Boolean(res?.items?.length)) {
        router.replace("/cart");
      }
    } catch (error) {
      console.log("error while fetching products", error);
    }

    if (status === "unauthenticated") {
      router.push(`/auth/login?callbackUrl=%2F${pathName}`);
    }

    if (status === "authenticated") {
      toast({
        variant: "success",
        title: "Added to cart",
        description: `${product.name} has been added to your cart`,
      });
    }

  };

  return (
    <div className="space-y-6">
      <Toaster />
      <div>
        <h1 className="text-3xl font-bold">{product.name}</h1>

        {/* Simple Brand Badge - Text only at the top */}
        {product.brand && (
          <div className="mt-2">
            <span className="inline-block bg-theme-accent-primary/90 text-white text-sm px-3 py-1 rounded-md shadow-sm">
              {typeof product.brand === 'string' ? product.brand : product.brand?.name || ''}
            </span>
          </div>
        )}

        {/* Only show ratings if there are any */}
        {product.average_rating > 0 && (
          <div className="flex items-center mt-2">
            <div className="flex items-center">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  className={`h-5 w-5 ${
                    i < Math.floor(product?.average_rating)
                      ? "fill-yellow-400 text-yellow-400"
                      : "text-gray-300"
                  }`}
                />
              ))}
            </div>
            {product.reviews > 0 && (
              <span className="ml-2 text-sm text-muted-foreground">
                ({product.reviews} reviews)
              </span>
            )}
          </div>
        )}
      </div>

      <p className="text-2xl font-bold">₹{product.price}</p>

      {/* Only show colors and sizes if they exist */}
      {(product.colors?.length > 0 || product.sizes?.length > 0) && (
        <div className="space-y-4">
          {/* Colors section - only show if colors exist */}
          {product.colors?.length > 0 && (
            <div>
              <label className="block text-sm font-medium mb-2">Color</label>
              <div className="flex gap-2">
                {product.colors.map((color) => (
                  <button
                    key={color}
                    onClick={() => onColorChange(color)}
                    className={`px-4 py-2 rounded-md border ${
                      selectedColor === color
                        ? "border-primary bg-primary/10"
                        : "border-input"
                    }`}
                  >
                    {color}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Sizes section - only show if sizes exist */}
          {product.sizes?.length > 0 && (
            <div>
              <label className="block text-sm font-medium mb-2">Size</label>
              <Select value={selectedSize} onValueChange={onSizeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  {product.sizes.map((size) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      )}

      <Button
        variant="outline"
        size="lg"
        className="w-auto px-4"
        onClick={handleAddToCart}
      >
        Add to Cart
      </Button>

      <div className="prose prose-sm max-w-none">
        {/* Product Description with Brand Logo */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4 sm:mb-6 gap-3 sm:gap-4">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Product Description</h2>

          {/* Enhanced Brand Logo */}
          {product.brand && typeof product.brand !== 'string' && (product.brand?.image_url || product.brand?.image) && (
            <div className="relative w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 overflow-hidden rounded-lg border border-gray-200 shadow-lg bg-white flex items-center justify-center p-1 sm:p-1.5 flex-shrink-0">
              <img
                src={product.brand?.image_url || `${MAIN_URL}${product.brand?.image}`}
                alt={`${product.brand?.name} logo`}
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  // Hide the image on error
                  const imgElement = e.currentTarget as HTMLImageElement;
                  imgElement.style.display = 'none';
                }}
              />
            </div>
          )}
        </div>

        {/* Use the new ProductDescription component */}
        <ProductDescription
          description={product.description}
          productName={product.name}
          productPrice={product.price}
          productBrand={typeof product.brand === 'string' ? product.brand : product.brand?.name}
        />
      </div>
    </div>
  );
};
