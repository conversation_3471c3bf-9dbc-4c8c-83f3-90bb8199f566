"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductDescription.tsx":
/*!***************************************************!*\
  !*** ./components/product/ProductDescription.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst ProductDescription = (param)=>{\n    let { description, productName = '', productPrice, productBrand } = param;\n    _s();\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        0,\n        1\n    ])); // Expand first two sections by default\n    const toggleSection = (index)=>{\n        const newExpanded = new Set(expandedSections);\n        if (newExpanded.has(index)) {\n            newExpanded.delete(index);\n        } else {\n            newExpanded.add(index);\n        }\n        setExpandedSections(newExpanded);\n    };\n    const parseSpecifications = (items)=>{\n        const specs = [];\n        items.forEach((item)=>{\n            // Try to parse key-value pairs (e.g., \"Power: 3 W\", \"Model: HW80\")\n            const colonIndex = item.indexOf(':');\n            if (colonIndex > 0 && colonIndex < item.length - 1) {\n                specs.push({\n                    key: item.substring(0, colonIndex).trim(),\n                    value: item.substring(colonIndex + 1).trim()\n                });\n                return;\n            }\n            // Try to extract power specifications (e.g., \"3 W\", \"1500 W\")\n            const powerMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*W/i);\n            if (powerMatch) {\n                specs.push({\n                    key: 'Power Consumption',\n                    value: powerMatch[0]\n                });\n                return;\n            }\n            // Try to extract model numbers (e.g., \"Model HW80\", \"HW80\")\n            const modelMatch = item.match(/(?:Model\\s+)?([A-Z]+\\d+[A-Z]*\\d*)/i);\n            if (modelMatch) {\n                specs.push({\n                    key: 'Model Number',\n                    value: modelMatch[1]\n                });\n                return;\n            }\n            // Try to extract capacity (e.g., \"8 kg\", \"10 L\")\n            const capacityMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|l|liters?|litres?)/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: \"\".concat(capacityMatch[1], \" \").concat(capacityMatch[2].toLowerCase())\n                });\n                return;\n            }\n            // Try to extract RPM (e.g., \"1400 RPM\")\n            const rpmMatch = item.match(/(\\d+)\\s*rpm/i);\n            if (rpmMatch) {\n                specs.push({\n                    key: 'Spin Speed',\n                    value: \"\".concat(rpmMatch[1], \" RPM\")\n                });\n                return;\n            }\n            // Try to extract dimensions\n            const dimensionMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*(cm|mm|m)/i);\n            if (dimensionMatch) {\n                specs.push({\n                    key: 'Dimensions',\n                    value: \"\".concat(dimensionMatch[1], \" x \").concat(dimensionMatch[2], \" x \").concat(dimensionMatch[3], \" \").concat(dimensionMatch[4])\n                });\n                return;\n            }\n            // Try to extract weight\n            const weightMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|g)/i);\n            if (weightMatch && !item.toLowerCase().includes('capacity')) {\n                specs.push({\n                    key: 'Weight',\n                    value: \"\".concat(weightMatch[1], \" \").concat(weightMatch[2])\n                });\n                return;\n            }\n            // If no specific pattern matches, treat as a general specification\n            if (item.trim()) {\n                specs.push({\n                    key: 'Feature',\n                    value: item.trim()\n                });\n            }\n        });\n        return specs;\n    };\n    const isGenericContent = (text)=>{\n        const genericPhrases = [\n            'high quality',\n            'reliable performance',\n            'durable construction',\n            'professional-grade performance and reliability',\n            'designed for optimal performance',\n            'excellent features and performance',\n            'quality product designed for reliable performance',\n            'high-quality construction ensures durability',\n            'reliable performance for consistent results',\n            'user-friendly design for easy operation',\n            'premium quality features',\n            'excellent value for money',\n            'designed for long-lasting performance'\n        ];\n        const lowerText = text.toLowerCase();\n        return genericPhrases.some((phrase)=>lowerText.includes(phrase));\n    };\n    const filterGenericItems = (items)=>{\n        return items.filter((item)=>!isGenericContent(item));\n    };\n    const extractSpecsFromProductName = (productName)=>{\n        const specs = [];\n        const name = productName.toLowerCase();\n        // Extract model number\n        const modelMatch = productName.match(/([A-Z]+\\d+[-\\w]*)/);\n        if (modelMatch) {\n            specs.push({\n                key: 'Model',\n                value: modelMatch[1]\n            });\n        }\n        // Extract dimensions\n        const dimensionMatch = productName.match(/(\\d+(?:\\.\\d+)?)\\s*(?:cm|mm|inch)/i);\n        if (dimensionMatch) {\n            specs.push({\n                key: 'Size',\n                value: dimensionMatch[0]\n            });\n        }\n        // Extract capacity for washing machines\n        if (name.includes('washing') || name.includes('washer')) {\n            const capacityMatch = productName.match(/(\\d+(?:\\.\\d+)?)\\s*kg/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: capacityMatch[0]\n                });\n            }\n        }\n        // Extract power ratings\n        const powerMatch = productName.match(/(\\d+)\\s*w/i);\n        if (powerMatch) {\n            specs.push({\n                key: 'Power',\n                value: \"\".concat(powerMatch[1], \" W\")\n            });\n        }\n        return specs;\n    };\n    const parseDescription = (desc)=>{\n        if (!desc || !desc.trim()) return null;\n        // Clean up the description and handle the actual format we receive\n        const cleanDesc = desc.trim();\n        // Extract overview (everything before the first section)\n        const overviewMatch = cleanDesc.match(RegExp(\"^(.*?)(?=\\\\s*(?:Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\\\s*•)\", \"s\"));\n        let overview = overviewMatch ? overviewMatch[1].trim() : '';\n        // Filter out generic overview content\n        if (isGenericContent(overview)) {\n            overview = ''; // Hide generic overviews\n        }\n        // Find all sections using regex that matches our actual format\n        const sectionRegex = /(Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\s*((?:•[^•]*)*)/g;\n        const sections = [];\n        let match;\n        while((match = sectionRegex.exec(cleanDesc)) !== null){\n            const title = match[1].trim();\n            const content = match[2].trim();\n            // Extract bullet points\n            const bulletRegex = /•\\s*([^•]+)/g;\n            const items = [];\n            let bulletMatch;\n            while((bulletMatch = bulletRegex.exec(content)) !== null){\n                const item = bulletMatch[1].trim();\n                if (item) {\n                    items.push(item);\n                }\n            }\n            if (items.length > 0) {\n                // Filter out generic items\n                const filteredItems = filterGenericItems(items);\n                // Only include section if it has non-generic content\n                if (filteredItems.length > 0) {\n                    // Determine section type\n                    let sectionType = 'list';\n                    if (title.toLowerCase().includes('specification')) {\n                        sectionType = 'specifications';\n                    } else if (title.toLowerCase().includes('feature')) {\n                        sectionType = 'features';\n                    }\n                    sections.push({\n                        title,\n                        items: filteredItems,\n                        type: sectionType\n                    });\n                }\n            }\n        }\n        return {\n            overview,\n            sections\n        };\n    };\n    const getSectionIcon = (title)=>{\n        const iconClass = \"w-5 h-5\";\n        switch(title.toLowerCase()){\n            case 'key features':\n            case 'features':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-amber-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 16\n                }, undefined);\n            case 'technical specifications':\n            case 'specifications':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-blue-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 16\n                }, undefined);\n            case 'benefits':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-green-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 16\n                }, undefined);\n            case 'ideal for':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-purple-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-gray-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const renderSpecificationTable = (items)=>{\n        const specs = parseSpecifications(items);\n        if (specs.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic text-center py-4\",\n                children: \"No specifications available\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden border border-gray-200 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-3 sm:px-4 lg:px-6 py-2 sm:py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-sm sm:text-base\",\n                        children: \"Technical Specifications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"divide-y divide-gray-200\",\n                    children: specs.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row hover:bg-blue-50 transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-2/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-gray-50 sm:border-r border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-semibold text-gray-800 block\",\n                                        children: spec.key\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-3/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-gray-900 font-medium block\",\n                                        children: spec.value || '—'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 px-3 sm:px-4 lg:px-6 py-1 sm:py-2 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 text-center\",\n                        children: \"Specifications may vary by model and region\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderFeatureGrid = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Key Features & Highlights\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-3 lg:gap-4\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200 hover:from-amber-100 hover:to-orange-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1 sm:mt-1.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 317,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderBenefitsList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Benefits & Advantages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 sm:space-y-3\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 hover:from-green-100 hover:to-emerald-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-green-600 mt-0.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderDefaultList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start p-2 hover:bg-gray-50 rounded-md transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-700\",\n                            children: item\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 359,\n            columnNumber: 7\n        }, undefined);\n    };\n    const generateFallbackContent = ()=>{\n        const specs = extractSpecsFromProductName(productName);\n        // Add price if available\n        if (productPrice) {\n            specs.push({\n                key: 'Price',\n                value: \"₹\".concat(productPrice.toLocaleString())\n            });\n        }\n        // Add brand if available\n        if (productBrand) {\n            specs.push({\n                key: 'Brand',\n                value: productBrand\n            });\n        }\n        // Generate dynamic overview based on product name and brand\n        const generateDynamicOverview = ()=>{\n            const name = productName.toLowerCase();\n            let overview = '';\n            if (productBrand) {\n                overview = \"The \".concat(productBrand, \" \").concat(productName);\n            } else {\n                overview = \"The \".concat(productName);\n            }\n            // Add specific descriptions based on product type\n            if (name.includes('lamp') || name.includes('light')) {\n                overview += ' provides efficient lighting for your space.';\n            } else if (name.includes('camera') || name.includes('dashcam')) {\n                overview += ' offers reliable recording and monitoring capabilities.';\n            } else if (name.includes('pump')) {\n                overview += ' delivers dependable inflation performance.';\n            } else if (name.includes('hood') || name.includes('chimney')) {\n                overview += ' ensures effective kitchen ventilation.';\n            } else if (name.includes('hinge') || name.includes('hardware')) {\n                overview += ' provides smooth and reliable operation.';\n            } else {\n                overview += ' is designed to meet your specific requirements.';\n            }\n            return overview;\n        };\n        return {\n            overview: generateDynamicOverview(),\n            sections: specs.length > 0 ? [\n                {\n                    title: 'Specifications',\n                    items: specs.map((spec)=>\"\".concat(spec.key, \": \").concat(spec.value)),\n                    type: 'specifications'\n                }\n            ] : []\n        };\n    };\n    const parsedDesc = parseDescription(description);\n    // If no meaningful content found, generate fallback\n    const finalDesc = parsedDesc && parsedDesc.sections.length > 0 ? parsedDesc : generateFallbackContent();\n    if (!finalDesc || finalDesc.sections.length === 0 && !finalDesc.overview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"prose prose-sm max-w-none\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: description || \"\".concat(productName, \" - Product information will be updated soon.\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 432,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 431,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-description space-y-4 sm:space-y-6\",\n        children: [\n            finalDesc.overview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overview-section bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 sm:p-6 border border-blue-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 mt-1 mr-2 sm:mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-base sm:text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"Product Overview\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed text-sm sm:text-base\",\n                                    children: finalDesc.overview\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 445,\n                columnNumber: 9\n            }, undefined),\n            finalDesc.sections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"description-section bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>toggleSection(index),\n                            className: \"w-full px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center min-w-0 flex-1\",\n                                    children: [\n                                        getSectionIcon(section.title),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 sm:ml-3 text-sm sm:text-lg font-semibold text-gray-900 truncate\",\n                                            children: section.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500 flex-shrink-0\",\n                                            children: [\n                                                \"(\",\n                                                section.items.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, undefined),\n                                expandedSections.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, undefined),\n                        expandedSections.has(index) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6\",\n                            children: [\n                                section.type === 'specifications' && renderSpecificationTable(section.items),\n                                section.type === 'features' && renderFeatureGrid(section.items),\n                                section.title.toLowerCase().includes('benefit') && renderBenefitsList(section.items),\n                                section.type === 'list' && !section.title.toLowerCase().includes('benefit') && renderDefaultList(section.items)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, undefined))\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n        lineNumber: 442,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductDescription, \"b/WJgSHj1mMZYb0GaMgxOeMj6ek=\");\n_c = ProductDescription;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDescription);\nvar _c;\n$RefreshReg$(_c, \"ProductDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcHJvZHVjdC9Qcm9kdWN0RGVzY3JpcHRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ21FO0FBeUIzRyxNQUFNVSxxQkFBd0Q7UUFBQyxFQUM3REMsV0FBVyxFQUNYQyxjQUFjLEVBQUUsRUFDaEJDLFlBQVksRUFDWkMsWUFBWSxFQUNiOztJQUNDLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR2YsK0NBQVFBLENBQWMsSUFBSWdCLElBQUk7UUFBQztRQUFHO0tBQUUsSUFBSSx1Q0FBdUM7SUFFL0gsTUFBTUMsZ0JBQWdCLENBQUNDO1FBQ3JCLE1BQU1DLGNBQWMsSUFBSUgsSUFBSUY7UUFDNUIsSUFBSUssWUFBWUMsR0FBRyxDQUFDRixRQUFRO1lBQzFCQyxZQUFZRSxNQUFNLENBQUNIO1FBQ3JCLE9BQU87WUFDTEMsWUFBWUcsR0FBRyxDQUFDSjtRQUNsQjtRQUNBSCxvQkFBb0JJO0lBQ3RCO0lBRUEsTUFBTUksc0JBQXNCLENBQUNDO1FBQzNCLE1BQU1DLFFBQTZCLEVBQUU7UUFFckNELE1BQU1FLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDWixtRUFBbUU7WUFDbkUsTUFBTUMsYUFBYUQsS0FBS0UsT0FBTyxDQUFDO1lBQ2hDLElBQUlELGFBQWEsS0FBS0EsYUFBYUQsS0FBS0csTUFBTSxHQUFHLEdBQUc7Z0JBQ2xETCxNQUFNTSxJQUFJLENBQUM7b0JBQ1RDLEtBQUtMLEtBQUtNLFNBQVMsQ0FBQyxHQUFHTCxZQUFZTSxJQUFJO29CQUN2Q0MsT0FBT1IsS0FBS00sU0FBUyxDQUFDTCxhQUFhLEdBQUdNLElBQUk7Z0JBQzVDO2dCQUNBO1lBQ0Y7WUFFQSw4REFBOEQ7WUFDOUQsTUFBTUUsYUFBYVQsS0FBS1UsS0FBSyxDQUFDO1lBQzlCLElBQUlELFlBQVk7Z0JBQ2RYLE1BQU1NLElBQUksQ0FBQztvQkFDVEMsS0FBSztvQkFDTEcsT0FBT0MsVUFBVSxDQUFDLEVBQUU7Z0JBQ3RCO2dCQUNBO1lBQ0Y7WUFFQSw0REFBNEQ7WUFDNUQsTUFBTUUsYUFBYVgsS0FBS1UsS0FBSyxDQUFDO1lBQzlCLElBQUlDLFlBQVk7Z0JBQ2RiLE1BQU1NLElBQUksQ0FBQztvQkFDVEMsS0FBSztvQkFDTEcsT0FBT0csVUFBVSxDQUFDLEVBQUU7Z0JBQ3RCO2dCQUNBO1lBQ0Y7WUFFQSxpREFBaUQ7WUFDakQsTUFBTUMsZ0JBQWdCWixLQUFLVSxLQUFLLENBQUM7WUFDakMsSUFBSUUsZUFBZTtnQkFDakJkLE1BQU1NLElBQUksQ0FBQztvQkFDVEMsS0FBSztvQkFDTEcsT0FBTyxHQUF1QkksT0FBcEJBLGFBQWEsQ0FBQyxFQUFFLEVBQUMsS0FBa0MsT0FBL0JBLGFBQWEsQ0FBQyxFQUFFLENBQUNDLFdBQVc7Z0JBQzVEO2dCQUNBO1lBQ0Y7WUFFQSx3Q0FBd0M7WUFDeEMsTUFBTUMsV0FBV2QsS0FBS1UsS0FBSyxDQUFDO1lBQzVCLElBQUlJLFVBQVU7Z0JBQ1poQixNQUFNTSxJQUFJLENBQUM7b0JBQ1RDLEtBQUs7b0JBQ0xHLE9BQU8sR0FBZSxPQUFaTSxRQUFRLENBQUMsRUFBRSxFQUFDO2dCQUN4QjtnQkFDQTtZQUNGO1lBRUEsNEJBQTRCO1lBQzVCLE1BQU1DLGlCQUFpQmYsS0FBS1UsS0FBSyxDQUFDO1lBQ2xDLElBQUlLLGdCQUFnQjtnQkFDbEJqQixNQUFNTSxJQUFJLENBQUM7b0JBQ1RDLEtBQUs7b0JBQ0xHLE9BQU8sR0FBMEJPLE9BQXZCQSxjQUFjLENBQUMsRUFBRSxFQUFDLE9BQTRCQSxPQUF2QkEsY0FBYyxDQUFDLEVBQUUsRUFBQyxPQUEwQkEsT0FBckJBLGNBQWMsQ0FBQyxFQUFFLEVBQUMsS0FBcUIsT0FBbEJBLGNBQWMsQ0FBQyxFQUFFO2dCQUNoRztnQkFDQTtZQUNGO1lBRUEsd0JBQXdCO1lBQ3hCLE1BQU1DLGNBQWNoQixLQUFLVSxLQUFLLENBQUM7WUFDL0IsSUFBSU0sZUFBZSxDQUFDaEIsS0FBS2EsV0FBVyxHQUFHSSxRQUFRLENBQUMsYUFBYTtnQkFDM0RuQixNQUFNTSxJQUFJLENBQUM7b0JBQ1RDLEtBQUs7b0JBQ0xHLE9BQU8sR0FBcUJRLE9BQWxCQSxXQUFXLENBQUMsRUFBRSxFQUFDLEtBQWtCLE9BQWZBLFdBQVcsQ0FBQyxFQUFFO2dCQUM1QztnQkFDQTtZQUNGO1lBRUEsbUVBQW1FO1lBQ25FLElBQUloQixLQUFLTyxJQUFJLElBQUk7Z0JBQ2ZULE1BQU1NLElBQUksQ0FBQztvQkFDVEMsS0FBSztvQkFDTEcsT0FBT1IsS0FBS08sSUFBSTtnQkFDbEI7WUFDRjtRQUNGO1FBRUEsT0FBT1Q7SUFDVDtJQUVBLE1BQU1vQixtQkFBbUIsQ0FBQ0M7UUFDeEIsTUFBTUMsaUJBQWlCO1lBQ3JCO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFFRCxNQUFNQyxZQUFZRixLQUFLTixXQUFXO1FBQ2xDLE9BQU9PLGVBQWVFLElBQUksQ0FBQ0MsQ0FBQUEsU0FBVUYsVUFBVUosUUFBUSxDQUFDTTtJQUMxRDtJQUVBLE1BQU1DLHFCQUFxQixDQUFDM0I7UUFDMUIsT0FBT0EsTUFBTTRCLE1BQU0sQ0FBQ3pCLENBQUFBLE9BQVEsQ0FBQ2tCLGlCQUFpQmxCO0lBQ2hEO0lBRUEsTUFBTTBCLDhCQUE4QixDQUFDMUM7UUFDbkMsTUFBTWMsUUFBNkIsRUFBRTtRQUNyQyxNQUFNNkIsT0FBTzNDLFlBQVk2QixXQUFXO1FBRXBDLHVCQUF1QjtRQUN2QixNQUFNRixhQUFhM0IsWUFBWTBCLEtBQUssQ0FBQztRQUNyQyxJQUFJQyxZQUFZO1lBQ2RiLE1BQU1NLElBQUksQ0FBQztnQkFBRUMsS0FBSztnQkFBU0csT0FBT0csVUFBVSxDQUFDLEVBQUU7WUFBQztRQUNsRDtRQUVBLHFCQUFxQjtRQUNyQixNQUFNSSxpQkFBaUIvQixZQUFZMEIsS0FBSyxDQUFDO1FBQ3pDLElBQUlLLGdCQUFnQjtZQUNsQmpCLE1BQU1NLElBQUksQ0FBQztnQkFBRUMsS0FBSztnQkFBUUcsT0FBT08sY0FBYyxDQUFDLEVBQUU7WUFBQztRQUNyRDtRQUVBLHdDQUF3QztRQUN4QyxJQUFJWSxLQUFLVixRQUFRLENBQUMsY0FBY1UsS0FBS1YsUUFBUSxDQUFDLFdBQVc7WUFDdkQsTUFBTUwsZ0JBQWdCNUIsWUFBWTBCLEtBQUssQ0FBQztZQUN4QyxJQUFJRSxlQUFlO2dCQUNqQmQsTUFBTU0sSUFBSSxDQUFDO29CQUFFQyxLQUFLO29CQUFZRyxPQUFPSSxhQUFhLENBQUMsRUFBRTtnQkFBQztZQUN4RDtRQUNGO1FBRUEsd0JBQXdCO1FBQ3hCLE1BQU1ILGFBQWF6QixZQUFZMEIsS0FBSyxDQUFDO1FBQ3JDLElBQUlELFlBQVk7WUFDZFgsTUFBTU0sSUFBSSxDQUFDO2dCQUFFQyxLQUFLO2dCQUFTRyxPQUFPLEdBQWlCLE9BQWRDLFVBQVUsQ0FBQyxFQUFFLEVBQUM7WUFBSTtRQUN6RDtRQUVBLE9BQU9YO0lBQ1Q7SUFFQSxNQUFNOEIsbUJBQW1CLENBQUNDO1FBQ3hCLElBQUksQ0FBQ0EsUUFBUSxDQUFDQSxLQUFLdEIsSUFBSSxJQUFJLE9BQU87UUFFbEMsbUVBQW1FO1FBQ25FLE1BQU11QixZQUFZRCxLQUFLdEIsSUFBSTtRQUUzQix5REFBeUQ7UUFDekQsTUFBTXdCLGdCQUFnQkQsVUFBVXBCLEtBQUssQ0FBQztRQUN0QyxJQUFJc0IsV0FBV0QsZ0JBQWdCQSxhQUFhLENBQUMsRUFBRSxDQUFDeEIsSUFBSSxLQUFLO1FBRXpELHNDQUFzQztRQUN0QyxJQUFJVyxpQkFBaUJjLFdBQVc7WUFDOUJBLFdBQVcsSUFBSSx5QkFBeUI7UUFDMUM7UUFFQSwrREFBK0Q7UUFDL0QsTUFBTUMsZUFBZTtRQUNyQixNQUFNQyxXQUE0QixFQUFFO1FBRXBDLElBQUl4QjtRQUNKLE1BQU8sQ0FBQ0EsUUFBUXVCLGFBQWFFLElBQUksQ0FBQ0wsVUFBUyxNQUFPLEtBQU07WUFDdEQsTUFBTU0sUUFBUTFCLEtBQUssQ0FBQyxFQUFFLENBQUNILElBQUk7WUFDM0IsTUFBTThCLFVBQVUzQixLQUFLLENBQUMsRUFBRSxDQUFDSCxJQUFJO1lBRTdCLHdCQUF3QjtZQUN4QixNQUFNK0IsY0FBYztZQUNwQixNQUFNekMsUUFBa0IsRUFBRTtZQUMxQixJQUFJMEM7WUFFSixNQUFPLENBQUNBLGNBQWNELFlBQVlILElBQUksQ0FBQ0UsUUFBTyxNQUFPLEtBQU07Z0JBQ3pELE1BQU1yQyxPQUFPdUMsV0FBVyxDQUFDLEVBQUUsQ0FBQ2hDLElBQUk7Z0JBQ2hDLElBQUlQLE1BQU07b0JBQ1JILE1BQU1PLElBQUksQ0FBQ0o7Z0JBQ2I7WUFDRjtZQUVBLElBQUlILE1BQU1NLE1BQU0sR0FBRyxHQUFHO2dCQUNwQiwyQkFBMkI7Z0JBQzNCLE1BQU1xQyxnQkFBZ0JoQixtQkFBbUIzQjtnQkFFekMscURBQXFEO2dCQUNyRCxJQUFJMkMsY0FBY3JDLE1BQU0sR0FBRyxHQUFHO29CQUM1Qix5QkFBeUI7b0JBQ3pCLElBQUlzQyxjQUFzRDtvQkFFMUQsSUFBSUwsTUFBTXZCLFdBQVcsR0FBR0ksUUFBUSxDQUFDLGtCQUFrQjt3QkFDakR3QixjQUFjO29CQUNoQixPQUFPLElBQUlMLE1BQU12QixXQUFXLEdBQUdJLFFBQVEsQ0FBQyxZQUFZO3dCQUNsRHdCLGNBQWM7b0JBQ2hCO29CQUVBUCxTQUFTOUIsSUFBSSxDQUFDO3dCQUNaZ0M7d0JBQ0F2QyxPQUFPMkM7d0JBQ1BFLE1BQU1EO29CQUNSO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLE9BQU87WUFBRVQ7WUFBVUU7UUFBUztJQUM5QjtJQUVBLE1BQU1TLGlCQUFpQixDQUFDUDtRQUN0QixNQUFNUSxZQUFZO1FBRWxCLE9BQVFSLE1BQU12QixXQUFXO1lBQ3ZCLEtBQUs7WUFDTCxLQUFLO2dCQUNILHFCQUFPLDhEQUFDdEMsZ0pBQUlBO29CQUFDc0UsV0FBVyxHQUFhLE9BQVZELFdBQVU7Ozs7OztZQUN2QyxLQUFLO1lBQ0wsS0FBSztnQkFDSCxxQkFBTyw4REFBQ25FLGdKQUFRQTtvQkFBQ29FLFdBQVcsR0FBYSxPQUFWRCxXQUFVOzs7Ozs7WUFDM0MsS0FBSztnQkFDSCxxQkFBTyw4REFBQy9ELGdKQUFLQTtvQkFBQ2dFLFdBQVcsR0FBYSxPQUFWRCxXQUFVOzs7Ozs7WUFDeEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3BFLGdKQUFNQTtvQkFBQ3FFLFdBQVcsR0FBYSxPQUFWRCxXQUFVOzs7Ozs7WUFDekM7Z0JBQ0UscUJBQU8sOERBQUNoRSxnSkFBT0E7b0JBQUNpRSxXQUFXLEdBQWEsT0FBVkQsV0FBVTs7Ozs7O1FBQzVDO0lBQ0Y7SUFFQSxNQUFNRSwyQkFBMkIsQ0FBQ2pEO1FBQ2hDLE1BQU1DLFFBQVFGLG9CQUFvQkM7UUFFbEMsSUFBSUMsTUFBTUssTUFBTSxLQUFLLEdBQUc7WUFDdEIscUJBQ0UsOERBQUM0QztnQkFBSUYsV0FBVTswQkFBd0M7Ozs7OztRQUkzRDtRQUVBLHFCQUNFLDhEQUFDRTtZQUFJRixXQUFVOzs4QkFFYiw4REFBQ0U7b0JBQUlGLFdBQVU7OEJBQ2IsNEVBQUNHO3dCQUFHSCxXQUFVO2tDQUFnRDs7Ozs7Ozs7Ozs7OEJBSWhFLDhEQUFDRTtvQkFBSUYsV0FBVTs4QkFDWi9DLE1BQU1tRCxHQUFHLENBQUMsQ0FBQ0MsTUFBTTNELHNCQUNoQiw4REFBQ3dEOzRCQUFnQkYsV0FBVTs7OENBQ3pCLDhEQUFDRTtvQ0FBSUYsV0FBVTs4Q0FDYiw0RUFBQ007d0NBQUtOLFdBQVU7a0RBQXdESyxLQUFLN0MsR0FBRzs7Ozs7Ozs7Ozs7OENBRWxGLDhEQUFDMEM7b0NBQUlGLFdBQVU7OENBQ2IsNEVBQUNNO3dDQUFLTixXQUFVO2tEQUNiSyxLQUFLMUMsS0FBSyxJQUFJOzs7Ozs7Ozs7Ozs7MkJBTlhqQjs7Ozs7Ozs7Ozs4QkFjZCw4REFBQ3dEO29CQUFJRixXQUFVOzhCQUNiLDRFQUFDTzt3QkFBRVAsV0FBVTtrQ0FBb0M7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTXpEO0lBRUEsTUFBTVEsb0JBQW9CLENBQUN4RDtRQUN6QixxQkFDRSw4REFBQ2tEO1lBQUlGLFdBQVU7OzhCQUViLDhEQUFDRTtvQkFBSUYsV0FBVTs4QkFDYiw0RUFBQ0c7d0JBQUdILFdBQVU7a0NBQThDOzs7Ozs7Ozs7Ozs4QkFJOUQsOERBQUNFO29CQUFJRixXQUFVOzhCQUNaaEQsTUFBTW9ELEdBQUcsQ0FBQyxDQUFDakQsTUFBTVQsc0JBQ2hCLDhEQUFDd0Q7NEJBQWdCRixXQUFVOzs4Q0FDekIsOERBQUNFO29DQUFJRixXQUFVOzs7Ozs7OENBQ2YsOERBQUNNO29DQUFLTixXQUFVOzhDQUFnRTdDOzs7Ozs7OzJCQUZ4RVQ7Ozs7Ozs7Ozs7Ozs7Ozs7SUFRcEI7SUFFQSxNQUFNK0QscUJBQXFCLENBQUN6RDtRQUMxQixxQkFDRSw4REFBQ2tEO1lBQUlGLFdBQVU7OzhCQUViLDhEQUFDRTtvQkFBSUYsV0FBVTs4QkFDYiw0RUFBQ0c7d0JBQUdILFdBQVU7a0NBQThDOzs7Ozs7Ozs7Ozs4QkFJOUQsOERBQUNFO29CQUFJRixXQUFVOzhCQUNaaEQsTUFBTW9ELEdBQUcsQ0FBQyxDQUFDakQsTUFBTVQsc0JBQ2hCLDhEQUFDd0Q7NEJBQWdCRixXQUFVOzs4Q0FDekIsOERBQUN2RSxnSkFBV0E7b0NBQUN1RSxXQUFVOzs7Ozs7OENBQ3ZCLDhEQUFDTTtvQ0FBS04sV0FBVTs4Q0FBZ0U3Qzs7Ozs7OzsyQkFGeEVUOzs7Ozs7Ozs7Ozs7Ozs7O0lBUXBCO0lBRUEsTUFBTWdFLG9CQUFvQixDQUFDMUQ7UUFDekIscUJBQ0UsOERBQUNrRDtZQUFJRixXQUFVO3NCQUNaaEQsTUFBTW9ELEdBQUcsQ0FBQyxDQUFDakQsTUFBTVQsc0JBQ2hCLDhEQUFDd0Q7b0JBQWdCRixXQUFVOztzQ0FDekIsOERBQUNFOzRCQUFJRixXQUFVOzs7Ozs7c0NBQ2YsOERBQUNNOzRCQUFLTixXQUFVO3NDQUF5QjdDOzs7Ozs7O21CQUZqQ1Q7Ozs7Ozs7Ozs7SUFPbEI7SUFFQSxNQUFNaUUsMEJBQTBCO1FBQzlCLE1BQU0xRCxRQUFRNEIsNEJBQTRCMUM7UUFFMUMseUJBQXlCO1FBQ3pCLElBQUlDLGNBQWM7WUFDaEJhLE1BQU1NLElBQUksQ0FBQztnQkFBRUMsS0FBSztnQkFBU0csT0FBTyxJQUFrQyxPQUE5QnZCLGFBQWF3RSxjQUFjO1lBQUs7UUFDeEU7UUFFQSx5QkFBeUI7UUFDekIsSUFBSXZFLGNBQWM7WUFDaEJZLE1BQU1NLElBQUksQ0FBQztnQkFBRUMsS0FBSztnQkFBU0csT0FBT3RCO1lBQWE7UUFDakQ7UUFFQSw0REFBNEQ7UUFDNUQsTUFBTXdFLDBCQUEwQjtZQUM5QixNQUFNL0IsT0FBTzNDLFlBQVk2QixXQUFXO1lBQ3BDLElBQUltQixXQUFXO1lBRWYsSUFBSTlDLGNBQWM7Z0JBQ2hCOEMsV0FBVyxPQUF1QmhELE9BQWhCRSxjQUFhLEtBQWUsT0FBWkY7WUFDcEMsT0FBTztnQkFDTGdELFdBQVcsT0FBbUIsT0FBWmhEO1lBQ3BCO1lBRUEsa0RBQWtEO1lBQ2xELElBQUkyQyxLQUFLVixRQUFRLENBQUMsV0FBV1UsS0FBS1YsUUFBUSxDQUFDLFVBQVU7Z0JBQ25EZSxZQUFZO1lBQ2QsT0FBTyxJQUFJTCxLQUFLVixRQUFRLENBQUMsYUFBYVUsS0FBS1YsUUFBUSxDQUFDLFlBQVk7Z0JBQzlEZSxZQUFZO1lBQ2QsT0FBTyxJQUFJTCxLQUFLVixRQUFRLENBQUMsU0FBUztnQkFDaENlLFlBQVk7WUFDZCxPQUFPLElBQUlMLEtBQUtWLFFBQVEsQ0FBQyxXQUFXVSxLQUFLVixRQUFRLENBQUMsWUFBWTtnQkFDNURlLFlBQVk7WUFDZCxPQUFPLElBQUlMLEtBQUtWLFFBQVEsQ0FBQyxZQUFZVSxLQUFLVixRQUFRLENBQUMsYUFBYTtnQkFDOURlLFlBQVk7WUFDZCxPQUFPO2dCQUNMQSxZQUFZO1lBQ2Q7WUFFQSxPQUFPQTtRQUNUO1FBRUEsT0FBTztZQUNMQSxVQUFVMEI7WUFDVnhCLFVBQVVwQyxNQUFNSyxNQUFNLEdBQUcsSUFBSTtnQkFBQztvQkFDNUJpQyxPQUFPO29CQUNQdkMsT0FBT0MsTUFBTW1ELEdBQUcsQ0FBQ0MsQ0FBQUEsT0FBUSxHQUFnQkEsT0FBYkEsS0FBSzdDLEdBQUcsRUFBQyxNQUFlLE9BQVg2QyxLQUFLMUMsS0FBSztvQkFDbkRrQyxNQUFNO2dCQUNSO2FBQUUsR0FBRyxFQUFFO1FBQ1Q7SUFDRjtJQUVBLE1BQU1pQixhQUFhL0IsaUJBQWlCN0M7SUFFcEMsb0RBQW9EO0lBQ3BELE1BQU02RSxZQUFZRCxjQUFjQSxXQUFXekIsUUFBUSxDQUFDL0IsTUFBTSxHQUFHLElBQ3pEd0QsYUFDQUg7SUFFSixJQUFJLENBQUNJLGFBQWNBLFVBQVUxQixRQUFRLENBQUMvQixNQUFNLEtBQUssS0FBSyxDQUFDeUQsVUFBVTVCLFFBQVEsRUFBRztRQUMxRSxxQkFDRSw4REFBQ2U7WUFBSUYsV0FBVTtzQkFDYiw0RUFBQ0U7Z0JBQUlGLFdBQVU7MEJBQ2IsNEVBQUNPO29CQUFFUCxXQUFVOzhCQUNWOUQsZUFBZSxHQUFlLE9BQVpDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLekM7SUFFQSxxQkFDRSw4REFBQytEO1FBQUlGLFdBQVU7O1lBRVplLFVBQVU1QixRQUFRLGtCQUNqQiw4REFBQ2U7Z0JBQUlGLFdBQVU7MEJBQ2IsNEVBQUNFO29CQUFJRixXQUFVOztzQ0FDYiw4REFBQ2pFLGdKQUFPQTs0QkFBQ2lFLFdBQVU7Ozs7OztzQ0FDbkIsOERBQUNFOzs4Q0FDQyw4REFBQ2M7b0NBQUdoQixXQUFVOzhDQUF3RDs7Ozs7OzhDQUN0RSw4REFBQ087b0NBQUVQLFdBQVU7OENBQ1ZlLFVBQVU1QixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVE1QjRCLFVBQVUxQixRQUFRLENBQUNlLEdBQUcsQ0FBQyxDQUFDYSxTQUFTdkUsc0JBQ2hDLDhEQUFDd0Q7b0JBQWdCRixXQUFVOztzQ0FFekIsOERBQUNrQjs0QkFDQ0MsU0FBUyxJQUFNMUUsY0FBY0M7NEJBQzdCc0QsV0FBVTs7OENBRVYsOERBQUNFO29DQUFJRixXQUFVOzt3Q0FDWkYsZUFBZW1CLFFBQVExQixLQUFLO3NEQUM3Qiw4REFBQ2U7NENBQUtOLFdBQVU7c0RBQXdFaUIsUUFBUTFCLEtBQUs7Ozs7OztzREFDckcsOERBQUNlOzRDQUFLTixXQUFVOztnREFBOEQ7Z0RBQUVpQixRQUFRakUsS0FBSyxDQUFDTSxNQUFNO2dEQUFDOzs7Ozs7Ozs7Ozs7O2dDQUV0R2hCLGlCQUFpQk0sR0FBRyxDQUFDRix1QkFDcEIsOERBQUNaLGdKQUFTQTtvQ0FBQ2tFLFdBQVU7Ozs7OzhEQUVyQiw4REFBQ25FLGdKQUFXQTtvQ0FBQ21FLFdBQVU7Ozs7Ozs7Ozs7Ozt3QkFLMUIxRCxpQkFBaUJNLEdBQUcsQ0FBQ0Ysd0JBQ3BCLDhEQUFDd0Q7NEJBQUlGLFdBQVU7O2dDQUNaaUIsUUFBUXBCLElBQUksS0FBSyxvQkFBb0JJLHlCQUF5QmdCLFFBQVFqRSxLQUFLO2dDQUMzRWlFLFFBQVFwQixJQUFJLEtBQUssY0FBY1csa0JBQWtCUyxRQUFRakUsS0FBSztnQ0FDOURpRSxRQUFRMUIsS0FBSyxDQUFDdkIsV0FBVyxHQUFHSSxRQUFRLENBQUMsY0FBY3FDLG1CQUFtQlEsUUFBUWpFLEtBQUs7Z0NBQ25GaUUsUUFBUXBCLElBQUksS0FBSyxVQUFVLENBQUNvQixRQUFRMUIsS0FBSyxDQUFDdkIsV0FBVyxHQUFHSSxRQUFRLENBQUMsY0FBY3NDLGtCQUFrQk8sUUFBUWpFLEtBQUs7Ozs7Ozs7O21CQXhCM0dOOzs7Ozs7Ozs7OztBQStCbEI7R0FoZE1UO0tBQUFBO0FBa2ROLGlFQUFlQSxrQkFBa0JBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXGNvbXBvbmVudHNcXHByb2R1Y3RcXFByb2R1Y3REZXNjcmlwdGlvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ2hlY2tDaXJjbGUsIFN0YXIsIFRhcmdldCwgU2V0dGluZ3MsIENoZXZyb25Eb3duLCBDaGV2cm9uVXAsIFBhY2thZ2UsIEF3YXJkIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuaW50ZXJmYWNlIFByb2R1Y3REZXNjcmlwdGlvblByb3BzIHtcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgcHJvZHVjdE5hbWU/OiBzdHJpbmc7XG4gIHByb2R1Y3RQcmljZT86IG51bWJlcjtcbiAgcHJvZHVjdEJyYW5kPzogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgUGFyc2VkU2VjdGlvbiB7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGl0ZW1zOiBzdHJpbmdbXTtcbiAgdHlwZTogJ2xpc3QnIHwgJ3NwZWNpZmljYXRpb25zJyB8ICdmZWF0dXJlcyc7XG59XG5cbmludGVyZmFjZSBQYXJzZWREZXNjcmlwdGlvbiB7XG4gIG92ZXJ2aWV3OiBzdHJpbmc7XG4gIHNlY3Rpb25zOiBQYXJzZWRTZWN0aW9uW107XG59XG5cbmludGVyZmFjZSBTcGVjaWZpY2F0aW9uSXRlbSB7XG4gIGtleTogc3RyaW5nO1xuICB2YWx1ZTogc3RyaW5nO1xufVxuXG5jb25zdCBQcm9kdWN0RGVzY3JpcHRpb246IFJlYWN0LkZDPFByb2R1Y3REZXNjcmlwdGlvblByb3BzPiA9ICh7XG4gIGRlc2NyaXB0aW9uLFxuICBwcm9kdWN0TmFtZSA9ICcnLFxuICBwcm9kdWN0UHJpY2UsXG4gIHByb2R1Y3RCcmFuZFxufSkgPT4ge1xuICBjb25zdCBbZXhwYW5kZWRTZWN0aW9ucywgc2V0RXhwYW5kZWRTZWN0aW9uc10gPSB1c2VTdGF0ZTxTZXQ8bnVtYmVyPj4obmV3IFNldChbMCwgMV0pKTsgLy8gRXhwYW5kIGZpcnN0IHR3byBzZWN0aW9ucyBieSBkZWZhdWx0XG5cbiAgY29uc3QgdG9nZ2xlU2VjdGlvbiA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgbmV3RXhwYW5kZWQgPSBuZXcgU2V0KGV4cGFuZGVkU2VjdGlvbnMpO1xuICAgIGlmIChuZXdFeHBhbmRlZC5oYXMoaW5kZXgpKSB7XG4gICAgICBuZXdFeHBhbmRlZC5kZWxldGUoaW5kZXgpO1xuICAgIH0gZWxzZSB7XG4gICAgICBuZXdFeHBhbmRlZC5hZGQoaW5kZXgpO1xuICAgIH1cbiAgICBzZXRFeHBhbmRlZFNlY3Rpb25zKG5ld0V4cGFuZGVkKTtcbiAgfTtcblxuICBjb25zdCBwYXJzZVNwZWNpZmljYXRpb25zID0gKGl0ZW1zOiBzdHJpbmdbXSk6IFNwZWNpZmljYXRpb25JdGVtW10gPT4ge1xuICAgIGNvbnN0IHNwZWNzOiBTcGVjaWZpY2F0aW9uSXRlbVtdID0gW107XG5cbiAgICBpdGVtcy5mb3JFYWNoKGl0ZW0gPT4ge1xuICAgICAgLy8gVHJ5IHRvIHBhcnNlIGtleS12YWx1ZSBwYWlycyAoZS5nLiwgXCJQb3dlcjogMyBXXCIsIFwiTW9kZWw6IEhXODBcIilcbiAgICAgIGNvbnN0IGNvbG9uSW5kZXggPSBpdGVtLmluZGV4T2YoJzonKTtcbiAgICAgIGlmIChjb2xvbkluZGV4ID4gMCAmJiBjb2xvbkluZGV4IDwgaXRlbS5sZW5ndGggLSAxKSB7XG4gICAgICAgIHNwZWNzLnB1c2goe1xuICAgICAgICAgIGtleTogaXRlbS5zdWJzdHJpbmcoMCwgY29sb25JbmRleCkudHJpbSgpLFxuICAgICAgICAgIHZhbHVlOiBpdGVtLnN1YnN0cmluZyhjb2xvbkluZGV4ICsgMSkudHJpbSgpXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFRyeSB0byBleHRyYWN0IHBvd2VyIHNwZWNpZmljYXRpb25zIChlLmcuLCBcIjMgV1wiLCBcIjE1MDAgV1wiKVxuICAgICAgY29uc3QgcG93ZXJNYXRjaCA9IGl0ZW0ubWF0Y2goLyhcXGQrKD86XFwuXFxkKyk/KVxccypXL2kpO1xuICAgICAgaWYgKHBvd2VyTWF0Y2gpIHtcbiAgICAgICAgc3BlY3MucHVzaCh7XG4gICAgICAgICAga2V5OiAnUG93ZXIgQ29uc3VtcHRpb24nLFxuICAgICAgICAgIHZhbHVlOiBwb3dlck1hdGNoWzBdXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFRyeSB0byBleHRyYWN0IG1vZGVsIG51bWJlcnMgKGUuZy4sIFwiTW9kZWwgSFc4MFwiLCBcIkhXODBcIilcbiAgICAgIGNvbnN0IG1vZGVsTWF0Y2ggPSBpdGVtLm1hdGNoKC8oPzpNb2RlbFxccyspPyhbQS1aXStcXGQrW0EtWl0qXFxkKikvaSk7XG4gICAgICBpZiAobW9kZWxNYXRjaCkge1xuICAgICAgICBzcGVjcy5wdXNoKHtcbiAgICAgICAgICBrZXk6ICdNb2RlbCBOdW1iZXInLFxuICAgICAgICAgIHZhbHVlOiBtb2RlbE1hdGNoWzFdXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFRyeSB0byBleHRyYWN0IGNhcGFjaXR5IChlLmcuLCBcIjgga2dcIiwgXCIxMCBMXCIpXG4gICAgICBjb25zdCBjYXBhY2l0eU1hdGNoID0gaXRlbS5tYXRjaCgvKFxcZCsoPzpcXC5cXGQrKT8pXFxzKihrZ3xsfGxpdGVycz98bGl0cmVzPykvaSk7XG4gICAgICBpZiAoY2FwYWNpdHlNYXRjaCkge1xuICAgICAgICBzcGVjcy5wdXNoKHtcbiAgICAgICAgICBrZXk6ICdDYXBhY2l0eScsXG4gICAgICAgICAgdmFsdWU6IGAke2NhcGFjaXR5TWF0Y2hbMV19ICR7Y2FwYWNpdHlNYXRjaFsyXS50b0xvd2VyQ2FzZSgpfWBcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gVHJ5IHRvIGV4dHJhY3QgUlBNIChlLmcuLCBcIjE0MDAgUlBNXCIpXG4gICAgICBjb25zdCBycG1NYXRjaCA9IGl0ZW0ubWF0Y2goLyhcXGQrKVxccypycG0vaSk7XG4gICAgICBpZiAocnBtTWF0Y2gpIHtcbiAgICAgICAgc3BlY3MucHVzaCh7XG4gICAgICAgICAga2V5OiAnU3BpbiBTcGVlZCcsXG4gICAgICAgICAgdmFsdWU6IGAke3JwbU1hdGNoWzFdfSBSUE1gXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFRyeSB0byBleHRyYWN0IGRpbWVuc2lvbnNcbiAgICAgIGNvbnN0IGRpbWVuc2lvbk1hdGNoID0gaXRlbS5tYXRjaCgvKFxcZCsoPzpcXC5cXGQrKT8pXFxzKnhcXHMqKFxcZCsoPzpcXC5cXGQrKT8pXFxzKnhcXHMqKFxcZCsoPzpcXC5cXGQrKT8pXFxzKihjbXxtbXxtKS9pKTtcbiAgICAgIGlmIChkaW1lbnNpb25NYXRjaCkge1xuICAgICAgICBzcGVjcy5wdXNoKHtcbiAgICAgICAgICBrZXk6ICdEaW1lbnNpb25zJyxcbiAgICAgICAgICB2YWx1ZTogYCR7ZGltZW5zaW9uTWF0Y2hbMV19IHggJHtkaW1lbnNpb25NYXRjaFsyXX0geCAke2RpbWVuc2lvbk1hdGNoWzNdfSAke2RpbWVuc2lvbk1hdGNoWzRdfWBcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gVHJ5IHRvIGV4dHJhY3Qgd2VpZ2h0XG4gICAgICBjb25zdCB3ZWlnaHRNYXRjaCA9IGl0ZW0ubWF0Y2goLyhcXGQrKD86XFwuXFxkKyk/KVxccyooa2d8ZykvaSk7XG4gICAgICBpZiAod2VpZ2h0TWF0Y2ggJiYgIWl0ZW0udG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnY2FwYWNpdHknKSkge1xuICAgICAgICBzcGVjcy5wdXNoKHtcbiAgICAgICAgICBrZXk6ICdXZWlnaHQnLFxuICAgICAgICAgIHZhbHVlOiBgJHt3ZWlnaHRNYXRjaFsxXX0gJHt3ZWlnaHRNYXRjaFsyXX1gXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIElmIG5vIHNwZWNpZmljIHBhdHRlcm4gbWF0Y2hlcywgdHJlYXQgYXMgYSBnZW5lcmFsIHNwZWNpZmljYXRpb25cbiAgICAgIGlmIChpdGVtLnRyaW0oKSkge1xuICAgICAgICBzcGVjcy5wdXNoKHtcbiAgICAgICAgICBrZXk6ICdGZWF0dXJlJyxcbiAgICAgICAgICB2YWx1ZTogaXRlbS50cmltKClcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gc3BlY3M7XG4gIH07XG5cbiAgY29uc3QgaXNHZW5lcmljQ29udGVudCA9ICh0ZXh0OiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgICBjb25zdCBnZW5lcmljUGhyYXNlcyA9IFtcbiAgICAgICdoaWdoIHF1YWxpdHknLFxuICAgICAgJ3JlbGlhYmxlIHBlcmZvcm1hbmNlJyxcbiAgICAgICdkdXJhYmxlIGNvbnN0cnVjdGlvbicsXG4gICAgICAncHJvZmVzc2lvbmFsLWdyYWRlIHBlcmZvcm1hbmNlIGFuZCByZWxpYWJpbGl0eScsXG4gICAgICAnZGVzaWduZWQgZm9yIG9wdGltYWwgcGVyZm9ybWFuY2UnLFxuICAgICAgJ2V4Y2VsbGVudCBmZWF0dXJlcyBhbmQgcGVyZm9ybWFuY2UnLFxuICAgICAgJ3F1YWxpdHkgcHJvZHVjdCBkZXNpZ25lZCBmb3IgcmVsaWFibGUgcGVyZm9ybWFuY2UnLFxuICAgICAgJ2hpZ2gtcXVhbGl0eSBjb25zdHJ1Y3Rpb24gZW5zdXJlcyBkdXJhYmlsaXR5JyxcbiAgICAgICdyZWxpYWJsZSBwZXJmb3JtYW5jZSBmb3IgY29uc2lzdGVudCByZXN1bHRzJyxcbiAgICAgICd1c2VyLWZyaWVuZGx5IGRlc2lnbiBmb3IgZWFzeSBvcGVyYXRpb24nLFxuICAgICAgJ3ByZW1pdW0gcXVhbGl0eSBmZWF0dXJlcycsXG4gICAgICAnZXhjZWxsZW50IHZhbHVlIGZvciBtb25leScsXG4gICAgICAnZGVzaWduZWQgZm9yIGxvbmctbGFzdGluZyBwZXJmb3JtYW5jZSdcbiAgICBdO1xuXG4gICAgY29uc3QgbG93ZXJUZXh0ID0gdGV4dC50b0xvd2VyQ2FzZSgpO1xuICAgIHJldHVybiBnZW5lcmljUGhyYXNlcy5zb21lKHBocmFzZSA9PiBsb3dlclRleHQuaW5jbHVkZXMocGhyYXNlKSk7XG4gIH07XG5cbiAgY29uc3QgZmlsdGVyR2VuZXJpY0l0ZW1zID0gKGl0ZW1zOiBzdHJpbmdbXSk6IHN0cmluZ1tdID0+IHtcbiAgICByZXR1cm4gaXRlbXMuZmlsdGVyKGl0ZW0gPT4gIWlzR2VuZXJpY0NvbnRlbnQoaXRlbSkpO1xuICB9O1xuXG4gIGNvbnN0IGV4dHJhY3RTcGVjc0Zyb21Qcm9kdWN0TmFtZSA9IChwcm9kdWN0TmFtZTogc3RyaW5nKTogU3BlY2lmaWNhdGlvbkl0ZW1bXSA9PiB7XG4gICAgY29uc3Qgc3BlY3M6IFNwZWNpZmljYXRpb25JdGVtW10gPSBbXTtcbiAgICBjb25zdCBuYW1lID0gcHJvZHVjdE5hbWUudG9Mb3dlckNhc2UoKTtcblxuICAgIC8vIEV4dHJhY3QgbW9kZWwgbnVtYmVyXG4gICAgY29uc3QgbW9kZWxNYXRjaCA9IHByb2R1Y3ROYW1lLm1hdGNoKC8oW0EtWl0rXFxkK1stXFx3XSopLyk7XG4gICAgaWYgKG1vZGVsTWF0Y2gpIHtcbiAgICAgIHNwZWNzLnB1c2goeyBrZXk6ICdNb2RlbCcsIHZhbHVlOiBtb2RlbE1hdGNoWzFdIH0pO1xuICAgIH1cblxuICAgIC8vIEV4dHJhY3QgZGltZW5zaW9uc1xuICAgIGNvbnN0IGRpbWVuc2lvbk1hdGNoID0gcHJvZHVjdE5hbWUubWF0Y2goLyhcXGQrKD86XFwuXFxkKyk/KVxccyooPzpjbXxtbXxpbmNoKS9pKTtcbiAgICBpZiAoZGltZW5zaW9uTWF0Y2gpIHtcbiAgICAgIHNwZWNzLnB1c2goeyBrZXk6ICdTaXplJywgdmFsdWU6IGRpbWVuc2lvbk1hdGNoWzBdIH0pO1xuICAgIH1cblxuICAgIC8vIEV4dHJhY3QgY2FwYWNpdHkgZm9yIHdhc2hpbmcgbWFjaGluZXNcbiAgICBpZiAobmFtZS5pbmNsdWRlcygnd2FzaGluZycpIHx8IG5hbWUuaW5jbHVkZXMoJ3dhc2hlcicpKSB7XG4gICAgICBjb25zdCBjYXBhY2l0eU1hdGNoID0gcHJvZHVjdE5hbWUubWF0Y2goLyhcXGQrKD86XFwuXFxkKyk/KVxccyprZy9pKTtcbiAgICAgIGlmIChjYXBhY2l0eU1hdGNoKSB7XG4gICAgICAgIHNwZWNzLnB1c2goeyBrZXk6ICdDYXBhY2l0eScsIHZhbHVlOiBjYXBhY2l0eU1hdGNoWzBdIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEV4dHJhY3QgcG93ZXIgcmF0aW5nc1xuICAgIGNvbnN0IHBvd2VyTWF0Y2ggPSBwcm9kdWN0TmFtZS5tYXRjaCgvKFxcZCspXFxzKncvaSk7XG4gICAgaWYgKHBvd2VyTWF0Y2gpIHtcbiAgICAgIHNwZWNzLnB1c2goeyBrZXk6ICdQb3dlcicsIHZhbHVlOiBgJHtwb3dlck1hdGNoWzFdfSBXYCB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gc3BlY3M7XG4gIH07XG5cbiAgY29uc3QgcGFyc2VEZXNjcmlwdGlvbiA9IChkZXNjOiBzdHJpbmcpOiBQYXJzZWREZXNjcmlwdGlvbiB8IG51bGwgPT4ge1xuICAgIGlmICghZGVzYyB8fCAhZGVzYy50cmltKCkpIHJldHVybiBudWxsO1xuXG4gICAgLy8gQ2xlYW4gdXAgdGhlIGRlc2NyaXB0aW9uIGFuZCBoYW5kbGUgdGhlIGFjdHVhbCBmb3JtYXQgd2UgcmVjZWl2ZVxuICAgIGNvbnN0IGNsZWFuRGVzYyA9IGRlc2MudHJpbSgpO1xuXG4gICAgLy8gRXh0cmFjdCBvdmVydmlldyAoZXZlcnl0aGluZyBiZWZvcmUgdGhlIGZpcnN0IHNlY3Rpb24pXG4gICAgY29uc3Qgb3ZlcnZpZXdNYXRjaCA9IGNsZWFuRGVzYy5tYXRjaCgvXiguKj8pKD89XFxzKig/OktleSBGZWF0dXJlc3xGZWF0dXJlc3xUZWNobmljYWwgU3BlY2lmaWNhdGlvbnN8U3BlY2lmaWNhdGlvbnN8QmVuZWZpdHN8SWRlYWwgRm9yKTpcXHMq4oCiKS9zKTtcbiAgICBsZXQgb3ZlcnZpZXcgPSBvdmVydmlld01hdGNoID8gb3ZlcnZpZXdNYXRjaFsxXS50cmltKCkgOiAnJztcblxuICAgIC8vIEZpbHRlciBvdXQgZ2VuZXJpYyBvdmVydmlldyBjb250ZW50XG4gICAgaWYgKGlzR2VuZXJpY0NvbnRlbnQob3ZlcnZpZXcpKSB7XG4gICAgICBvdmVydmlldyA9ICcnOyAvLyBIaWRlIGdlbmVyaWMgb3ZlcnZpZXdzXG4gICAgfVxuXG4gICAgLy8gRmluZCBhbGwgc2VjdGlvbnMgdXNpbmcgcmVnZXggdGhhdCBtYXRjaGVzIG91ciBhY3R1YWwgZm9ybWF0XG4gICAgY29uc3Qgc2VjdGlvblJlZ2V4ID0gLyhLZXkgRmVhdHVyZXN8RmVhdHVyZXN8VGVjaG5pY2FsIFNwZWNpZmljYXRpb25zfFNwZWNpZmljYXRpb25zfEJlbmVmaXRzfElkZWFsIEZvcik6XFxzKigoPzrigKJbXuKAol0qKSopL2c7XG4gICAgY29uc3Qgc2VjdGlvbnM6IFBhcnNlZFNlY3Rpb25bXSA9IFtdO1xuXG4gICAgbGV0IG1hdGNoO1xuICAgIHdoaWxlICgobWF0Y2ggPSBzZWN0aW9uUmVnZXguZXhlYyhjbGVhbkRlc2MpKSAhPT0gbnVsbCkge1xuICAgICAgY29uc3QgdGl0bGUgPSBtYXRjaFsxXS50cmltKCk7XG4gICAgICBjb25zdCBjb250ZW50ID0gbWF0Y2hbMl0udHJpbSgpO1xuXG4gICAgICAvLyBFeHRyYWN0IGJ1bGxldCBwb2ludHNcbiAgICAgIGNvbnN0IGJ1bGxldFJlZ2V4ID0gL+KAolxccyooW17igKJdKykvZztcbiAgICAgIGNvbnN0IGl0ZW1zOiBzdHJpbmdbXSA9IFtdO1xuICAgICAgbGV0IGJ1bGxldE1hdGNoO1xuXG4gICAgICB3aGlsZSAoKGJ1bGxldE1hdGNoID0gYnVsbGV0UmVnZXguZXhlYyhjb250ZW50KSkgIT09IG51bGwpIHtcbiAgICAgICAgY29uc3QgaXRlbSA9IGJ1bGxldE1hdGNoWzFdLnRyaW0oKTtcbiAgICAgICAgaWYgKGl0ZW0pIHtcbiAgICAgICAgICBpdGVtcy5wdXNoKGl0ZW0pO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChpdGVtcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIC8vIEZpbHRlciBvdXQgZ2VuZXJpYyBpdGVtc1xuICAgICAgICBjb25zdCBmaWx0ZXJlZEl0ZW1zID0gZmlsdGVyR2VuZXJpY0l0ZW1zKGl0ZW1zKTtcblxuICAgICAgICAvLyBPbmx5IGluY2x1ZGUgc2VjdGlvbiBpZiBpdCBoYXMgbm9uLWdlbmVyaWMgY29udGVudFxuICAgICAgICBpZiAoZmlsdGVyZWRJdGVtcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgLy8gRGV0ZXJtaW5lIHNlY3Rpb24gdHlwZVxuICAgICAgICAgIGxldCBzZWN0aW9uVHlwZTogJ2xpc3QnIHwgJ3NwZWNpZmljYXRpb25zJyB8ICdmZWF0dXJlcycgPSAnbGlzdCc7XG5cbiAgICAgICAgICBpZiAodGl0bGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnc3BlY2lmaWNhdGlvbicpKSB7XG4gICAgICAgICAgICBzZWN0aW9uVHlwZSA9ICdzcGVjaWZpY2F0aW9ucyc7XG4gICAgICAgICAgfSBlbHNlIGlmICh0aXRsZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdmZWF0dXJlJykpIHtcbiAgICAgICAgICAgIHNlY3Rpb25UeXBlID0gJ2ZlYXR1cmVzJztcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBzZWN0aW9ucy5wdXNoKHtcbiAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgaXRlbXM6IGZpbHRlcmVkSXRlbXMsXG4gICAgICAgICAgICB0eXBlOiBzZWN0aW9uVHlwZVxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgb3ZlcnZpZXcsIHNlY3Rpb25zIH07XG4gIH07XG5cbiAgY29uc3QgZ2V0U2VjdGlvbkljb24gPSAodGl0bGU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGljb25DbGFzcyA9IFwidy01IGgtNVwiO1xuXG4gICAgc3dpdGNoICh0aXRsZS50b0xvd2VyQ2FzZSgpKSB7XG4gICAgICBjYXNlICdrZXkgZmVhdHVyZXMnOlxuICAgICAgY2FzZSAnZmVhdHVyZXMnOlxuICAgICAgICByZXR1cm4gPFN0YXIgY2xhc3NOYW1lPXtgJHtpY29uQ2xhc3N9IHRleHQtYW1iZXItNjAwYH0gLz47XG4gICAgICBjYXNlICd0ZWNobmljYWwgc3BlY2lmaWNhdGlvbnMnOlxuICAgICAgY2FzZSAnc3BlY2lmaWNhdGlvbnMnOlxuICAgICAgICByZXR1cm4gPFNldHRpbmdzIGNsYXNzTmFtZT17YCR7aWNvbkNsYXNzfSB0ZXh0LWJsdWUtNjAwYH0gLz47XG4gICAgICBjYXNlICdiZW5lZml0cyc6XG4gICAgICAgIHJldHVybiA8QXdhcmQgY2xhc3NOYW1lPXtgJHtpY29uQ2xhc3N9IHRleHQtZ3JlZW4tNjAwYH0gLz47XG4gICAgICBjYXNlICdpZGVhbCBmb3InOlxuICAgICAgICByZXR1cm4gPFRhcmdldCBjbGFzc05hbWU9e2Ake2ljb25DbGFzc30gdGV4dC1wdXJwbGUtNjAwYH0gLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPFBhY2thZ2UgY2xhc3NOYW1lPXtgJHtpY29uQ2xhc3N9IHRleHQtZ3JheS02MDBgfSAvPjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVuZGVyU3BlY2lmaWNhdGlvblRhYmxlID0gKGl0ZW1zOiBzdHJpbmdbXSkgPT4ge1xuICAgIGNvbnN0IHNwZWNzID0gcGFyc2VTcGVjaWZpY2F0aW9ucyhpdGVtcyk7XG5cbiAgICBpZiAoc3BlY3MubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaXRhbGljIHRleHQtY2VudGVyIHB5LTRcIj5cbiAgICAgICAgICBObyBzcGVjaWZpY2F0aW9ucyBhdmFpbGFibGVcbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGJvcmRlciBib3JkZXItZ3JheS0yMDAgc2hhZG93LXNtXCI+XG4gICAgICAgIHsvKiBUYWJsZSBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWJsdWUtNzAwIHB4LTMgc206cHgtNCBsZzpweC02IHB5LTIgc206cHktM1wiPlxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdGV4dC1zbSBzbTp0ZXh0LWJhc2VcIj5UZWNobmljYWwgU3BlY2lmaWNhdGlvbnM8L2g0PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU3BlY2lmaWNhdGlvbnMgVGFibGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAge3NwZWNzLm1hcCgoc3BlYywgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBob3ZlcjpiZy1ibHVlLTUwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNtOnctMi81IHB4LTMgc206cHgtNCBsZzpweC02IHB5LTIgc206cHktMyBsZzpweS00IGJnLWdyYXktNTAgc206Ym9yZGVyLXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBibG9ja1wiPntzcGVjLmtleX08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNtOnctMy81IHB4LTMgc206cHgtNCBsZzpweC02IHB5LTIgc206cHktMyBsZzpweS00IGJnLXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JheS05MDAgZm9udC1tZWRpdW0gYmxvY2tcIj5cbiAgICAgICAgICAgICAgICAgIHtzcGVjLnZhbHVlIHx8ICfigJQnfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvb3RlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHB4LTMgc206cHgtNCBsZzpweC02IHB5LTEgc206cHktMiBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIFNwZWNpZmljYXRpb25zIG1heSB2YXJ5IGJ5IG1vZGVsIGFuZCByZWdpb25cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfTtcblxuICBjb25zdCByZW5kZXJGZWF0dXJlR3JpZCA9IChpdGVtczogc3RyaW5nW10pID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTMgc206c3BhY2UteS00XCI+XG4gICAgICAgIHsvKiBGZWF0dXJlcyBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWFtYmVyLTUwMCB0by1vcmFuZ2UtNTAwIHB4LTMgc206cHgtNCBweS0yIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkIHRleHQteHMgc206dGV4dC1zbVwiPktleSBGZWF0dXJlcyAmIEhpZ2hsaWdodHM8L2g0PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRmVhdHVyZXMgR3JpZCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC0yIHNtOmdhcC0zIGxnOmdhcC00XCI+XG4gICAgICAgICAge2l0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBwLTMgc206cC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1hbWJlci01MCB0by1vcmFuZ2UtNTAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWFtYmVyLTIwMCBob3Zlcjpmcm9tLWFtYmVyLTEwMCBob3Zlcjp0by1vcmFuZ2UtMTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBzaGFkb3ctc21cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIHNtOnctMyBzbTpoLTMgYmctZ3JhZGllbnQtdG8tciBmcm9tLWFtYmVyLTUwMCB0by1vcmFuZ2UtNTAwIHJvdW5kZWQtZnVsbCBtdC0xIHNtOm10LTEuNSBtci0yIHNtOm1yLTMgZmxleC1zaHJpbmstMFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1ncmF5LTgwMCBmb250LW1lZGl1bSBsZWFkaW5nLXJlbGF4ZWRcIj57aXRlbX08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IHJlbmRlckJlbmVmaXRzTGlzdCA9IChpdGVtczogc3RyaW5nW10pID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTMgc206c3BhY2UteS00XCI+XG4gICAgICAgIHsvKiBCZW5lZml0cyBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTYwMCB0by1lbWVyYWxkLTYwMCBweC0zIHNtOnB4LTQgcHktMiByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCB0ZXh0LXhzIHNtOnRleHQtc21cIj5CZW5lZml0cyAmIEFkdmFudGFnZXM8L2g0PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQmVuZWZpdHMgTGlzdCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgc206c3BhY2UteS0zXCI+XG4gICAgICAgICAge2l0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBwLTMgc206cC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MCB0by1lbWVyYWxkLTUwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDAgaG92ZXI6ZnJvbS1ncmVlbi0xMDAgaG92ZXI6dG8tZW1lcmFsZC0xMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHNoYWRvdy1zbVwiPlxuICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01IHRleHQtZ3JlZW4tNjAwIG10LTAuNSBtci0yIHNtOm1yLTMgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC1zbSB0ZXh0LWdyYXktODAwIGZvbnQtbWVkaXVtIGxlYWRpbmctcmVsYXhlZFwiPntpdGVtfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgY29uc3QgcmVuZGVyRGVmYXVsdExpc3QgPSAoaXRlbXM6IHN0cmluZ1tdKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgIHtpdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHAtMiBob3ZlcjpiZy1ncmF5LTUwIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmF5LTQwMCByb3VuZGVkLWZ1bGwgbXQtMiBtci0zIGZsZXgtc2hyaW5rLTBcIj48L2Rpdj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMFwiPntpdGVtfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IGdlbmVyYXRlRmFsbGJhY2tDb250ZW50ID0gKCkgPT4ge1xuICAgIGNvbnN0IHNwZWNzID0gZXh0cmFjdFNwZWNzRnJvbVByb2R1Y3ROYW1lKHByb2R1Y3ROYW1lKTtcblxuICAgIC8vIEFkZCBwcmljZSBpZiBhdmFpbGFibGVcbiAgICBpZiAocHJvZHVjdFByaWNlKSB7XG4gICAgICBzcGVjcy5wdXNoKHsga2V5OiAnUHJpY2UnLCB2YWx1ZTogYOKCuSR7cHJvZHVjdFByaWNlLnRvTG9jYWxlU3RyaW5nKCl9YCB9KTtcbiAgICB9XG5cbiAgICAvLyBBZGQgYnJhbmQgaWYgYXZhaWxhYmxlXG4gICAgaWYgKHByb2R1Y3RCcmFuZCkge1xuICAgICAgc3BlY3MucHVzaCh7IGtleTogJ0JyYW5kJywgdmFsdWU6IHByb2R1Y3RCcmFuZCB9KTtcbiAgICB9XG5cbiAgICAvLyBHZW5lcmF0ZSBkeW5hbWljIG92ZXJ2aWV3IGJhc2VkIG9uIHByb2R1Y3QgbmFtZSBhbmQgYnJhbmRcbiAgICBjb25zdCBnZW5lcmF0ZUR5bmFtaWNPdmVydmlldyA9ICgpID0+IHtcbiAgICAgIGNvbnN0IG5hbWUgPSBwcm9kdWN0TmFtZS50b0xvd2VyQ2FzZSgpO1xuICAgICAgbGV0IG92ZXJ2aWV3ID0gJyc7XG5cbiAgICAgIGlmIChwcm9kdWN0QnJhbmQpIHtcbiAgICAgICAgb3ZlcnZpZXcgPSBgVGhlICR7cHJvZHVjdEJyYW5kfSAke3Byb2R1Y3ROYW1lfWA7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBvdmVydmlldyA9IGBUaGUgJHtwcm9kdWN0TmFtZX1gO1xuICAgICAgfVxuXG4gICAgICAvLyBBZGQgc3BlY2lmaWMgZGVzY3JpcHRpb25zIGJhc2VkIG9uIHByb2R1Y3QgdHlwZVxuICAgICAgaWYgKG5hbWUuaW5jbHVkZXMoJ2xhbXAnKSB8fCBuYW1lLmluY2x1ZGVzKCdsaWdodCcpKSB7XG4gICAgICAgIG92ZXJ2aWV3ICs9ICcgcHJvdmlkZXMgZWZmaWNpZW50IGxpZ2h0aW5nIGZvciB5b3VyIHNwYWNlLic7XG4gICAgICB9IGVsc2UgaWYgKG5hbWUuaW5jbHVkZXMoJ2NhbWVyYScpIHx8IG5hbWUuaW5jbHVkZXMoJ2Rhc2hjYW0nKSkge1xuICAgICAgICBvdmVydmlldyArPSAnIG9mZmVycyByZWxpYWJsZSByZWNvcmRpbmcgYW5kIG1vbml0b3JpbmcgY2FwYWJpbGl0aWVzLic7XG4gICAgICB9IGVsc2UgaWYgKG5hbWUuaW5jbHVkZXMoJ3B1bXAnKSkge1xuICAgICAgICBvdmVydmlldyArPSAnIGRlbGl2ZXJzIGRlcGVuZGFibGUgaW5mbGF0aW9uIHBlcmZvcm1hbmNlLic7XG4gICAgICB9IGVsc2UgaWYgKG5hbWUuaW5jbHVkZXMoJ2hvb2QnKSB8fCBuYW1lLmluY2x1ZGVzKCdjaGltbmV5JykpIHtcbiAgICAgICAgb3ZlcnZpZXcgKz0gJyBlbnN1cmVzIGVmZmVjdGl2ZSBraXRjaGVuIHZlbnRpbGF0aW9uLic7XG4gICAgICB9IGVsc2UgaWYgKG5hbWUuaW5jbHVkZXMoJ2hpbmdlJykgfHwgbmFtZS5pbmNsdWRlcygnaGFyZHdhcmUnKSkge1xuICAgICAgICBvdmVydmlldyArPSAnIHByb3ZpZGVzIHNtb290aCBhbmQgcmVsaWFibGUgb3BlcmF0aW9uLic7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBvdmVydmlldyArPSAnIGlzIGRlc2lnbmVkIHRvIG1lZXQgeW91ciBzcGVjaWZpYyByZXF1aXJlbWVudHMuJztcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIG92ZXJ2aWV3O1xuICAgIH07XG5cbiAgICByZXR1cm4ge1xuICAgICAgb3ZlcnZpZXc6IGdlbmVyYXRlRHluYW1pY092ZXJ2aWV3KCksXG4gICAgICBzZWN0aW9uczogc3BlY3MubGVuZ3RoID4gMCA/IFt7XG4gICAgICAgIHRpdGxlOiAnU3BlY2lmaWNhdGlvbnMnLFxuICAgICAgICBpdGVtczogc3BlY3MubWFwKHNwZWMgPT4gYCR7c3BlYy5rZXl9OiAke3NwZWMudmFsdWV9YCksXG4gICAgICAgIHR5cGU6ICdzcGVjaWZpY2F0aW9ucycgYXMgY29uc3RcbiAgICAgIH1dIDogW11cbiAgICB9O1xuICB9O1xuXG4gIGNvbnN0IHBhcnNlZERlc2MgPSBwYXJzZURlc2NyaXB0aW9uKGRlc2NyaXB0aW9uKTtcblxuICAvLyBJZiBubyBtZWFuaW5nZnVsIGNvbnRlbnQgZm91bmQsIGdlbmVyYXRlIGZhbGxiYWNrXG4gIGNvbnN0IGZpbmFsRGVzYyA9IHBhcnNlZERlc2MgJiYgcGFyc2VkRGVzYy5zZWN0aW9ucy5sZW5ndGggPiAwXG4gICAgPyBwYXJzZWREZXNjXG4gICAgOiBnZW5lcmF0ZUZhbGxiYWNrQ29udGVudCgpO1xuXG4gIGlmICghZmluYWxEZXNjIHx8IChmaW5hbERlc2Muc2VjdGlvbnMubGVuZ3RoID09PSAwICYmICFmaW5hbERlc2Mub3ZlcnZpZXcpKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvc2UgcHJvc2Utc20gbWF4LXctbm9uZVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcm91bmRlZC1sZyBwLTQgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICB7ZGVzY3JpcHRpb24gfHwgYCR7cHJvZHVjdE5hbWV9IC0gUHJvZHVjdCBpbmZvcm1hdGlvbiB3aWxsIGJlIHVwZGF0ZWQgc29vbi5gfVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInByb2R1Y3QtZGVzY3JpcHRpb24gc3BhY2UteS00IHNtOnNwYWNlLXktNlwiPlxuICAgICAgey8qIE92ZXJ2aWV3IFNlY3Rpb24gKi99XG4gICAgICB7ZmluYWxEZXNjLm92ZXJ2aWV3ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVydmlldy1zZWN0aW9uIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby01MCByb3VuZGVkLWxnIHAtNCBzbTpwLTYgYm9yZGVyIGJvcmRlci1ibHVlLTEwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydFwiPlxuICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy01IGgtNSBzbTp3LTYgc206aC02IHRleHQtYmx1ZS02MDAgbXQtMSBtci0yIHNtOm1yLTMgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1iYXNlIHNtOnRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5Qcm9kdWN0IE92ZXJ2aWV3PC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBsZWFkaW5nLXJlbGF4ZWQgdGV4dC1zbSBzbTp0ZXh0LWJhc2VcIj5cbiAgICAgICAgICAgICAgICB7ZmluYWxEZXNjLm92ZXJ2aWV3fVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogRHluYW1pYyBTZWN0aW9ucyAqL31cbiAgICAgIHtmaW5hbERlc2Muc2VjdGlvbnMubWFwKChzZWN0aW9uLCBpbmRleCkgPT4gKFxuICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImRlc2NyaXB0aW9uLXNlY3Rpb24gYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIG92ZXJmbG93LWhpZGRlbiBzaGFkb3ctc21cIj5cbiAgICAgICAgICB7LyogU2VjdGlvbiBIZWFkZXIgKi99XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlU2VjdGlvbihpbmRleCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBzbTpweC02IHB5LTMgc206cHktNCBiZy1ncmF5LTUwIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWluLXctMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAge2dldFNlY3Rpb25JY29uKHNlY3Rpb24udGl0bGUpfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHNtOm1sLTMgdGV4dC1zbSBzbTp0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPntzZWN0aW9uLnRpdGxlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMSBzbTptbC0yIHRleHQteHMgc206dGV4dC1zbSB0ZXh0LWdyYXktNTAwIGZsZXgtc2hyaW5rLTBcIj4oe3NlY3Rpb24uaXRlbXMubGVuZ3RofSk8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHtleHBhbmRlZFNlY3Rpb25zLmhhcyhpbmRleCkgPyAoXG4gICAgICAgICAgICAgIDxDaGV2cm9uVXAgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01IHRleHQtZ3JheS01MDAgZmxleC1zaHJpbmstMCBtbC0yXCIgLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxDaGV2cm9uRG93biBjbGFzc05hbWU9XCJ3LTQgaC00IHNtOnctNSBzbTpoLTUgdGV4dC1ncmF5LTUwMCBmbGV4LXNocmluay0wIG1sLTJcIiAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIHsvKiBTZWN0aW9uIENvbnRlbnQgKi99XG4gICAgICAgICAge2V4cGFuZGVkU2VjdGlvbnMuaGFzKGluZGV4KSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBzbTpwLTZcIj5cbiAgICAgICAgICAgICAge3NlY3Rpb24udHlwZSA9PT0gJ3NwZWNpZmljYXRpb25zJyAmJiByZW5kZXJTcGVjaWZpY2F0aW9uVGFibGUoc2VjdGlvbi5pdGVtcyl9XG4gICAgICAgICAgICAgIHtzZWN0aW9uLnR5cGUgPT09ICdmZWF0dXJlcycgJiYgcmVuZGVyRmVhdHVyZUdyaWQoc2VjdGlvbi5pdGVtcyl9XG4gICAgICAgICAgICAgIHtzZWN0aW9uLnRpdGxlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2JlbmVmaXQnKSAmJiByZW5kZXJCZW5lZml0c0xpc3Qoc2VjdGlvbi5pdGVtcyl9XG4gICAgICAgICAgICAgIHtzZWN0aW9uLnR5cGUgPT09ICdsaXN0JyAmJiAhc2VjdGlvbi50aXRsZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdiZW5lZml0JykgJiYgcmVuZGVyRGVmYXVsdExpc3Qoc2VjdGlvbi5pdGVtcyl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUHJvZHVjdERlc2NyaXB0aW9uO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJDaGVja0NpcmNsZSIsIlN0YXIiLCJUYXJnZXQiLCJTZXR0aW5ncyIsIkNoZXZyb25Eb3duIiwiQ2hldnJvblVwIiwiUGFja2FnZSIsIkF3YXJkIiwiUHJvZHVjdERlc2NyaXB0aW9uIiwiZGVzY3JpcHRpb24iLCJwcm9kdWN0TmFtZSIsInByb2R1Y3RQcmljZSIsInByb2R1Y3RCcmFuZCIsImV4cGFuZGVkU2VjdGlvbnMiLCJzZXRFeHBhbmRlZFNlY3Rpb25zIiwiU2V0IiwidG9nZ2xlU2VjdGlvbiIsImluZGV4IiwibmV3RXhwYW5kZWQiLCJoYXMiLCJkZWxldGUiLCJhZGQiLCJwYXJzZVNwZWNpZmljYXRpb25zIiwiaXRlbXMiLCJzcGVjcyIsImZvckVhY2giLCJpdGVtIiwiY29sb25JbmRleCIsImluZGV4T2YiLCJsZW5ndGgiLCJwdXNoIiwia2V5Iiwic3Vic3RyaW5nIiwidHJpbSIsInZhbHVlIiwicG93ZXJNYXRjaCIsIm1hdGNoIiwibW9kZWxNYXRjaCIsImNhcGFjaXR5TWF0Y2giLCJ0b0xvd2VyQ2FzZSIsInJwbU1hdGNoIiwiZGltZW5zaW9uTWF0Y2giLCJ3ZWlnaHRNYXRjaCIsImluY2x1ZGVzIiwiaXNHZW5lcmljQ29udGVudCIsInRleHQiLCJnZW5lcmljUGhyYXNlcyIsImxvd2VyVGV4dCIsInNvbWUiLCJwaHJhc2UiLCJmaWx0ZXJHZW5lcmljSXRlbXMiLCJmaWx0ZXIiLCJleHRyYWN0U3BlY3NGcm9tUHJvZHVjdE5hbWUiLCJuYW1lIiwicGFyc2VEZXNjcmlwdGlvbiIsImRlc2MiLCJjbGVhbkRlc2MiLCJvdmVydmlld01hdGNoIiwib3ZlcnZpZXciLCJzZWN0aW9uUmVnZXgiLCJzZWN0aW9ucyIsImV4ZWMiLCJ0aXRsZSIsImNvbnRlbnQiLCJidWxsZXRSZWdleCIsImJ1bGxldE1hdGNoIiwiZmlsdGVyZWRJdGVtcyIsInNlY3Rpb25UeXBlIiwidHlwZSIsImdldFNlY3Rpb25JY29uIiwiaWNvbkNsYXNzIiwiY2xhc3NOYW1lIiwicmVuZGVyU3BlY2lmaWNhdGlvblRhYmxlIiwiZGl2IiwiaDQiLCJtYXAiLCJzcGVjIiwic3BhbiIsInAiLCJyZW5kZXJGZWF0dXJlR3JpZCIsInJlbmRlckJlbmVmaXRzTGlzdCIsInJlbmRlckRlZmF1bHRMaXN0IiwiZ2VuZXJhdGVGYWxsYmFja0NvbnRlbnQiLCJ0b0xvY2FsZVN0cmluZyIsImdlbmVyYXRlRHluYW1pY092ZXJ2aWV3IiwicGFyc2VkRGVzYyIsImZpbmFsRGVzYyIsImgzIiwic2VjdGlvbiIsImJ1dHRvbiIsIm9uQ2xpY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductDescription.tsx\n"));

/***/ })

});