"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductDescription.tsx":
/*!***************************************************!*\
  !*** ./components/product/ProductDescription.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst ProductDescription = (param)=>{\n    let { description, productName = '', productPrice, productBrand } = param;\n    _s();\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        0,\n        1\n    ])); // Expand first two sections by default\n    const toggleSection = (index)=>{\n        const newExpanded = new Set(expandedSections);\n        if (newExpanded.has(index)) {\n            newExpanded.delete(index);\n        } else {\n            newExpanded.add(index);\n        }\n        setExpandedSections(newExpanded);\n    };\n    const parseSpecifications = (items)=>{\n        const specs = [];\n        items.forEach((item)=>{\n            // Try to parse key-value pairs (e.g., \"Power: 3 W\", \"Model: HW80\")\n            const colonIndex = item.indexOf(':');\n            if (colonIndex > 0 && colonIndex < item.length - 1) {\n                specs.push({\n                    key: item.substring(0, colonIndex).trim(),\n                    value: item.substring(colonIndex + 1).trim()\n                });\n                return;\n            }\n            // Try to extract power specifications (e.g., \"3 W\", \"1500 W\")\n            const powerMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*W/i);\n            if (powerMatch) {\n                specs.push({\n                    key: 'Power Consumption',\n                    value: powerMatch[0]\n                });\n                return;\n            }\n            // Try to extract model numbers (e.g., \"Model HW80\", \"HW80\")\n            const modelMatch = item.match(/(?:Model\\s+)?([A-Z]+\\d+[A-Z]*\\d*)/i);\n            if (modelMatch) {\n                specs.push({\n                    key: 'Model Number',\n                    value: modelMatch[1]\n                });\n                return;\n            }\n            // Try to extract capacity (e.g., \"8 kg\", \"10 L\")\n            const capacityMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|l|liters?|litres?)/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: \"\".concat(capacityMatch[1], \" \").concat(capacityMatch[2].toLowerCase())\n                });\n                return;\n            }\n            // Try to extract RPM (e.g., \"1400 RPM\")\n            const rpmMatch = item.match(/(\\d+)\\s*rpm/i);\n            if (rpmMatch) {\n                specs.push({\n                    key: 'Spin Speed',\n                    value: \"\".concat(rpmMatch[1], \" RPM\")\n                });\n                return;\n            }\n            // Try to extract dimensions\n            const dimensionMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*(cm|mm|m)/i);\n            if (dimensionMatch) {\n                specs.push({\n                    key: 'Dimensions',\n                    value: \"\".concat(dimensionMatch[1], \" x \").concat(dimensionMatch[2], \" x \").concat(dimensionMatch[3], \" \").concat(dimensionMatch[4])\n                });\n                return;\n            }\n            // Try to extract weight\n            const weightMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|g)/i);\n            if (weightMatch && !item.toLowerCase().includes('capacity')) {\n                specs.push({\n                    key: 'Weight',\n                    value: \"\".concat(weightMatch[1], \" \").concat(weightMatch[2])\n                });\n                return;\n            }\n            // If no specific pattern matches, treat as a general specification\n            if (item.trim()) {\n                specs.push({\n                    key: 'Feature',\n                    value: item.trim()\n                });\n            }\n        });\n        return specs;\n    };\n    const isGenericContent = (text)=>{\n        const genericPhrases = [\n            'high quality',\n            'reliable performance',\n            'durable construction',\n            'professional-grade performance and reliability',\n            'designed for optimal performance',\n            'excellent features and performance'\n        ];\n        const lowerText = text.toLowerCase();\n        return genericPhrases.some((phrase)=>lowerText.includes(phrase));\n    };\n    const filterGenericItems = (items)=>{\n        return items.filter((item)=>!isGenericContent(item));\n    };\n    const extractSpecsFromProductName = (productName)=>{\n        const specs = [];\n        const name = productName.toLowerCase();\n        // Extract model number\n        const modelMatch = productName.match(/([A-Z]+\\d+[-\\w]*)/);\n        if (modelMatch) {\n            specs.push({\n                key: 'Model',\n                value: modelMatch[1]\n            });\n        }\n        // Extract dimensions\n        const dimensionMatch = productName.match(/(\\d+(?:\\.\\d+)?)\\s*(?:cm|mm|inch)/i);\n        if (dimensionMatch) {\n            specs.push({\n                key: 'Size',\n                value: dimensionMatch[0]\n            });\n        }\n        // Extract capacity for washing machines\n        if (name.includes('washing') || name.includes('washer')) {\n            const capacityMatch = productName.match(/(\\d+(?:\\.\\d+)?)\\s*kg/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: capacityMatch[0]\n                });\n            }\n        }\n        // Extract power ratings\n        const powerMatch = productName.match(/(\\d+)\\s*w/i);\n        if (powerMatch) {\n            specs.push({\n                key: 'Power',\n                value: \"\".concat(powerMatch[1], \" W\")\n            });\n        }\n        return specs;\n    };\n    const parseDescription = (desc)=>{\n        if (!desc || !desc.trim()) return null;\n        // Clean up the description and handle the actual format we receive\n        const cleanDesc = desc.trim();\n        // Extract overview (everything before the first section)\n        const overviewMatch = cleanDesc.match(RegExp(\"^(.*?)(?=\\\\s*(?:Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\\\s*•)\", \"s\"));\n        let overview = overviewMatch ? overviewMatch[1].trim() : '';\n        // Filter out generic overview content\n        if (isGenericContent(overview)) {\n            overview = ''; // Hide generic overviews\n        }\n        // Find all sections using regex that matches our actual format\n        const sectionRegex = /(Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\s*((?:•[^•]*)*)/g;\n        const sections = [];\n        let match;\n        while((match = sectionRegex.exec(cleanDesc)) !== null){\n            const title = match[1].trim();\n            const content = match[2].trim();\n            // Extract bullet points\n            const bulletRegex = /•\\s*([^•]+)/g;\n            const items = [];\n            let bulletMatch;\n            while((bulletMatch = bulletRegex.exec(content)) !== null){\n                const item = bulletMatch[1].trim();\n                if (item) {\n                    items.push(item);\n                }\n            }\n            if (items.length > 0) {\n                // Filter out generic items\n                const filteredItems = filterGenericItems(items);\n                // Only include section if it has non-generic content\n                if (filteredItems.length > 0) {\n                    // Determine section type\n                    let sectionType = 'list';\n                    if (title.toLowerCase().includes('specification')) {\n                        sectionType = 'specifications';\n                    } else if (title.toLowerCase().includes('feature')) {\n                        sectionType = 'features';\n                    }\n                    sections.push({\n                        title,\n                        items: filteredItems,\n                        type: sectionType\n                    });\n                }\n            }\n        }\n        return {\n            overview,\n            sections\n        };\n    };\n    const getSectionIcon = (title)=>{\n        const iconClass = \"w-5 h-5\";\n        switch(title.toLowerCase()){\n            case 'key features':\n            case 'features':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-amber-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 16\n                }, undefined);\n            case 'technical specifications':\n            case 'specifications':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-blue-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 16\n                }, undefined);\n            case 'benefits':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-green-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 16\n                }, undefined);\n            case 'ideal for':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-purple-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-gray-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const renderSpecificationTable = (items)=>{\n        const specs = parseSpecifications(items);\n        if (specs.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic text-center py-4\",\n                children: \"No specifications available\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden border border-gray-200 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-3 sm:px-4 lg:px-6 py-2 sm:py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-sm sm:text-base\",\n                        children: \"Technical Specifications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"divide-y divide-gray-200\",\n                    children: specs.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row hover:bg-blue-50 transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-2/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-gray-50 sm:border-r border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-semibold text-gray-800 block\",\n                                        children: spec.key\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-3/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-gray-900 font-medium block\",\n                                        children: spec.value || '—'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 px-3 sm:px-4 lg:px-6 py-1 sm:py-2 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 text-center\",\n                        children: \"Specifications may vary by model and region\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderFeatureGrid = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Key Features & Highlights\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-3 lg:gap-4\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200 hover:from-amber-100 hover:to-orange-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1 sm:mt-1.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 310,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderBenefitsList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Benefits & Advantages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 sm:space-y-3\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 hover:from-green-100 hover:to-emerald-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-green-600 mt-0.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderDefaultList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start p-2 hover:bg-gray-50 rounded-md transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-700\",\n                            children: item\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, undefined);\n    };\n    const parsedDesc = parseDescription(description);\n    if (!parsedDesc) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"prose prose-sm max-w-none\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 368,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 367,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-description space-y-4 sm:space-y-6\",\n        children: [\n            parsedDesc.overview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overview-section bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 sm:p-6 border border-blue-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 mt-1 mr-2 sm:mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-base sm:text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"Product Overview\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed text-sm sm:text-base\",\n                                    children: parsedDesc.overview\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 379,\n                columnNumber: 9\n            }, undefined),\n            parsedDesc.sections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"description-section bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>toggleSection(index),\n                            className: \"w-full px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center min-w-0 flex-1\",\n                                    children: [\n                                        getSectionIcon(section.title),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 sm:ml-3 text-sm sm:text-lg font-semibold text-gray-900 truncate\",\n                                            children: section.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500 flex-shrink-0\",\n                                            children: [\n                                                \"(\",\n                                                section.items.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, undefined),\n                                expandedSections.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, undefined),\n                        expandedSections.has(index) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6\",\n                            children: [\n                                section.type === 'specifications' && renderSpecificationTable(section.items),\n                                section.type === 'features' && renderFeatureGrid(section.items),\n                                section.title.toLowerCase().includes('benefit') && renderBenefitsList(section.items),\n                                section.type === 'list' && !section.title.toLowerCase().includes('benefit') && renderDefaultList(section.items)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, undefined))\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n        lineNumber: 376,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductDescription, \"b/WJgSHj1mMZYb0GaMgxOeMj6ek=\");\n_c = ProductDescription;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDescription);\nvar _c;\n$RefreshReg$(_c, \"ProductDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductDescription.tsx\n"));

/***/ })

});