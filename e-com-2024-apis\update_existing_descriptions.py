#!/usr/bin/env python3
"""
Selective Product Description Update Script

This script selectively updates existing product descriptions in the database
using enhanced descriptions from generated JSON files. It only updates products
that already have descriptions, preserving products without descriptions.

Features:
- Updates only existing product descriptions (selective updating)
- Preserves products with no descriptions (doesn't add new descriptions)
- Provides quality score filtering for targeted updates
- Comprehensive backup and rollback functionality
- Database connection management using .env configuration
- Detailed logging and progress tracking

Author: Triumph Enterprise
Created: 2025
Updated: 2025 - Enhanced for selective updating
"""

import os
import sys
import django
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")
django.setup()

from django.db import transaction, connection
from products.models import Product
from django.core.exceptions import ObjectDoesNotExist

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('selective_description_update.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SelectiveDescriptionUpdater:
    """Handles selective updating of existing product descriptions"""
    
    def __init__(self):
        self.updated_count = 0
        self.skipped_count = 0
        self.failed_count = 0
        self.preserved_count = 0  # Products without descriptions that were preserved
        self.backup_data = []
    
    def load_enhanced_descriptions_json(self, filepath: str) -> Dict[str, Any]:
        """Load enhanced descriptions from JSON file"""
        try:
            logger.info(f"Loading enhanced descriptions from: {filepath}")
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'products' not in data:
                raise ValueError("JSON file must contain 'products' key")
            
            products = data['products']
            logger.info(f"Loaded {len(products)} enhanced descriptions from JSON file")
            
            return data
            
        except Exception as e:
            logger.error(f"Error loading JSON file {filepath}: {str(e)}")
            raise
    
    def create_selective_backup(self, product_ids: List[int]) -> str:
        """Create backup of current descriptions for products being updated"""
        try:
            logger.info("Creating backup of current product descriptions...")
            
            # Get current descriptions only for products that have descriptions
            products = Product.objects.filter(
                id__in=product_ids,
                description__isnull=False
            ).exclude(description='').values(
                'id', 'name', 'description', 'updated_at'
            )
            
            backup_data = {
                "backup_created_at": datetime.now().isoformat(),
                "backup_type": "selective_update",
                "total_products": len(products),
                "note": "Only products with existing descriptions are included in this backup",
                "products": list(products)
            }
            
            # Save backup file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"selective_description_backup_{timestamp}.json"
            backup_filepath = os.path.join(
                os.path.dirname(__file__), 
                "data new qubo jsons", 
                backup_filename
            )
            
            os.makedirs(os.path.dirname(backup_filepath), exist_ok=True)
            
            with open(backup_filepath, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Selective backup created: {backup_filepath}")
            logger.info(f"Backed up {len(products)} products with existing descriptions")
            return backup_filepath
            
        except Exception as e:
            logger.error(f"Error creating selective backup: {str(e)}")
            raise
    
    def should_update_product(self, product: Product, enhanced_data: Dict[str, Any], 
                            min_quality_score: int = 6) -> bool:
        """Determine if a product should be updated based on selective criteria"""
        try:
            # Only update products that already have descriptions
            if not product.description or product.description.strip() == '':
                return False
            
            # Check if enhanced description quality score meets threshold
            quality_score = enhanced_data.get('description_quality_score', 0)
            if quality_score >= min_quality_score:
                # Product already has good quality, skip
                return False
            
            # Check if enhanced description is actually better
            enhanced_desc = enhanced_data.get('enhanced_description', '')
            if not enhanced_desc or len(enhanced_desc.strip()) < len(product.description.strip()):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error evaluating update criteria for product {product.name}: {str(e)}")
            return False
    
    def update_single_product_description(self, product_data: Dict[str, Any], 
                                        min_quality_score: int = 6) -> str:
        """Update a single product's description with selective logic"""
        try:
            product_id = product_data.get('id')
            enhanced_description = product_data.get('enhanced_description')
            product_name = product_data.get('name', 'Unknown')
            
            if not product_id or not enhanced_description:
                logger.warning(f"Missing required data for product: {product_name}")
                return "failed"
            
            # Get the product
            try:
                product = Product.objects.get(id=product_id)
            except ObjectDoesNotExist:
                logger.warning(f"Product with ID {product_id} not found in database")
                return "failed"
            
            # Apply selective update logic
            if not self.should_update_product(product, product_data, min_quality_score):
                if not product.description or product.description.strip() == '':
                    logger.info(f"Preserving product without description: {product.name}")
                    return "preserved"
                else:
                    logger.info(f"Skipping product with good description: {product.name}")
                    return "skipped"
            
            # Store backup data
            self.backup_data.append({
                'id': product.id,
                'name': product.name,
                'old_description': product.description,
                'new_description': enhanced_description,
                'old_quality_score': product_data.get('description_quality_score', 0)
            })
            
            # Update description
            old_description = product.description
            product.description = enhanced_description
            product.save(update_fields=['description', 'updated_at'])
            
            logger.info(f"Updated description for product: {product.name} (ID: {product_id})")
            logger.debug(f"Old description length: {len(old_description or '')}")
            logger.debug(f"New description length: {len(enhanced_description)}")
            
            return "updated"
            
        except Exception as e:
            logger.error(f"Error updating product {product_data.get('name', 'Unknown')}: {str(e)}")
            return "failed"

    def selective_update_from_json(self, json_filepath: str, min_quality_score: int = 6,
                                 dry_run: bool = False) -> Dict[str, int]:
        """Selectively update product descriptions from JSON file"""
        try:
            # Load JSON data
            json_data = self.load_enhanced_descriptions_json(json_filepath)
            products = json_data['products']

            if dry_run:
                logger.info("DRY RUN MODE - No actual updates will be performed")

            # Get product IDs for backup (only those with existing descriptions)
            product_ids = [p.get('id') for p in products if p.get('id')]

            # Create backup if not dry run
            backup_filepath = None
            if not dry_run and product_ids:
                backup_filepath = self.create_selective_backup(product_ids)

            # Reset counters
            self.updated_count = 0
            self.skipped_count = 0
            self.failed_count = 0
            self.preserved_count = 0

            # Process products in batches
            batch_size = 50
            total_products = len(products)

            logger.info(f"Starting selective processing of {total_products} products...")
            logger.info(f"Quality threshold: {min_quality_score}/10")
            logger.info("Only products with existing descriptions will be considered for updates")

            with transaction.atomic():
                for i in range(0, total_products, batch_size):
                    batch = products[i:i + batch_size]
                    logger.info(f"Processing batch {i//batch_size + 1} ({len(batch)} products)")

                    for product_data in batch:
                        try:
                            if dry_run:
                                # Simulate selective update logic in dry run
                                product_id = product_data.get('id')
                                enhanced_description = product_data.get('enhanced_description')

                                if product_id and enhanced_description:
                                    try:
                                        product = Product.objects.get(id=product_id)
                                        if self.should_update_product(product, product_data, min_quality_score):
                                            self.updated_count += 1
                                            logger.debug(f"DRY RUN: Would update product ID {product_id}")
                                        elif not product.description or product.description.strip() == '':
                                            self.preserved_count += 1
                                            logger.debug(f"DRY RUN: Would preserve product without description ID {product_id}")
                                        else:
                                            self.skipped_count += 1
                                            logger.debug(f"DRY RUN: Would skip product with good description ID {product_id}")
                                    except ObjectDoesNotExist:
                                        self.failed_count += 1
                                        logger.debug(f"DRY RUN: Product ID {product_id} not found")
                                else:
                                    self.failed_count += 1
                                    logger.debug(f"DRY RUN: Invalid data for product")
                            else:
                                # Actual selective update
                                result = self.update_single_product_description(product_data, min_quality_score)
                                if result == "updated":
                                    self.updated_count += 1
                                elif result == "skipped":
                                    self.skipped_count += 1
                                elif result == "preserved":
                                    self.preserved_count += 1
                                else:  # failed
                                    self.failed_count += 1

                        except Exception as e:
                            logger.error(f"Error processing product in batch: {str(e)}")
                            self.failed_count += 1
                            continue

                    # Log progress
                    progress = ((i + len(batch)) / total_products) * 100
                    logger.info(f"Progress: {progress:.1f}% ({i + len(batch)}/{total_products})")

            # Prepare results
            results = {
                'total_processed': total_products,
                'updated': self.updated_count,
                'skipped': self.skipped_count,
                'preserved': self.preserved_count,
                'failed': self.failed_count,
                'backup_file': backup_filepath,
                'quality_threshold': min_quality_score
            }

            return results

        except Exception as e:
            logger.error(f"Error in selective update from JSON: {str(e)}")
            raise

    def find_latest_enhanced_json_file(self, directory: str) -> Optional[str]:
        """Find the latest enhanced descriptions JSON file"""
        try:
            json_dir = Path(directory)
            if not json_dir.exists():
                logger.error(f"Directory not found: {directory}")
                return None

            # Look for enhanced descriptions JSON files
            json_files = list(json_dir.glob("enhanced_descriptions_*.json"))

            if not json_files:
                logger.error(f"No enhanced descriptions JSON files found in {directory}")
                return None

            # Sort by modification time and get the latest
            latest_file = max(json_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"Found latest enhanced descriptions file: {latest_file}")

            return str(latest_file)

        except Exception as e:
            logger.error(f"Error finding latest enhanced descriptions file: {str(e)}")
            return None

    def validate_database_connection(self) -> bool:
        """Validate database connection"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    logger.info("Database connection validated successfully")
                    return True
                else:
                    logger.error("Database connection validation failed")
                    return False
        except Exception as e:
            logger.error(f"Database connection error: {str(e)}")
            return False

def main():
    """Main function to run the selective description update"""
    try:
        logger.info("Starting Selective Product Description Update Script...")
        logger.info("This script updates ONLY existing product descriptions, preserving products without descriptions")

        # Validate database connection
        updater = SelectiveDescriptionUpdater()
        if not updater.validate_database_connection():
            logger.error("Cannot proceed without valid database connection")
            return

        # Find the latest enhanced descriptions JSON file
        json_directory = os.path.join(os.path.dirname(__file__), "data new qubo jsons")
        json_filepath = updater.find_latest_enhanced_json_file(json_directory)

        if not json_filepath:
            logger.error("No enhanced descriptions JSON file found to process")
            print("\nNo enhanced descriptions JSON file found!")
            print("Please run 'generate_enhanced_descriptions.py' first to create enhanced descriptions.")
            return

        # Get quality threshold from user
        print(f"\nFound enhanced descriptions file: {json_filepath}")
        print("\nSelective Update Mode:")
        print("- Only products with existing descriptions will be updated")
        print("- Products without descriptions will be preserved (not modified)")
        print("- Only products below the quality threshold will be updated")

        try:
            threshold_input = input("\nEnter quality threshold for updates (default: 6): ").strip()
            min_quality_score = int(threshold_input) if threshold_input else 6
            min_quality_score = max(1, min(10, min_quality_score))
        except ValueError:
            min_quality_score = 6
            print("Invalid input, using default threshold of 6")

        print(f"\nUsing quality threshold: {min_quality_score}/10")
        print("Products with quality score >= threshold will be skipped")

        # Perform dry run first
        print("\nPerforming dry run to analyze impact...")
        dry_run_results = updater.selective_update_from_json(json_filepath, min_quality_score, dry_run=True)

        print(f"\nDry run results:")
        print(f"- Total products in file: {dry_run_results['total_processed']}")
        print(f"- Would update (existing descriptions): {dry_run_results['updated']}")
        print(f"- Would skip (good descriptions): {dry_run_results['skipped']}")
        print(f"- Would preserve (no descriptions): {dry_run_results['preserved']}")
        print(f"- Would fail (errors): {dry_run_results['failed']}")

        if dry_run_results['updated'] == 0:
            print("\nNo products need updating with the current criteria!")
            print("All existing descriptions are already good quality.")
            return

        if dry_run_results['failed'] > 0:
            print(f"\nWarning: {dry_run_results['failed']} products have issues!")

        # Show selective update summary
        print(f"\nSelective Update Summary:")
        print(f"- Products to update: {dry_run_results['updated']}")
        print(f"- Products to preserve: {dry_run_results['preserved']}")
        print(f"- Products to skip: {dry_run_results['skipped']}")

        # Ask for confirmation
        response = input(f"\nProceed with selective update of {dry_run_results['updated']} products? (y/N): ").strip().lower()
        if response != 'y':
            print("Selective update cancelled by user")
            return

        # Perform actual selective update
        print("\nPerforming selective update...")
        results = updater.selective_update_from_json(json_filepath, min_quality_score, dry_run=False)

        # Print comprehensive summary
        print("\n" + "="*70)
        print("SELECTIVE PRODUCT DESCRIPTION UPDATE SUMMARY")
        print("="*70)
        print(f"Update mode: Selective (existing descriptions only)")
        print(f"Quality threshold: {results['quality_threshold']}/10")
        print(f"Total products processed: {results['total_processed']}")
        print(f"Successfully updated: {results['updated']}")
        print(f"Skipped (good quality): {results['skipped']}")
        print(f"Preserved (no description): {results['preserved']}")
        print(f"Failed updates: {results['failed']}")
        if results['backup_file']:
            print(f"Backup file: {results['backup_file']}")
        print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\nUpdate Strategy:")
        print("✓ Updated only products with existing poor-quality descriptions")
        print("✓ Preserved products without descriptions (no new descriptions added)")
        print("✓ Skipped products with already good descriptions")
        print("✓ Created backup for rollback if needed")
        print("="*70)

        logger.info("Selective product description update completed successfully!")

    except Exception as e:
        logger.error(f"Script execution failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
