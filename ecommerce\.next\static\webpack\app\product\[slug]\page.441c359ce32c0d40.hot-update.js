"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductDescription.tsx":
/*!***************************************************!*\
  !*** ./components/product/ProductDescription.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,ChevronDown,ChevronUp,Package,Settings,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst ProductDescription = (param)=>{\n    let { description, productName = '', productPrice, productBrand } = param;\n    _s();\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        0,\n        1\n    ])); // Expand first two sections by default\n    const toggleSection = (index)=>{\n        const newExpanded = new Set(expandedSections);\n        if (newExpanded.has(index)) {\n            newExpanded.delete(index);\n        } else {\n            newExpanded.add(index);\n        }\n        setExpandedSections(newExpanded);\n    };\n    const parseSpecifications = (items)=>{\n        const specs = [];\n        items.forEach((item)=>{\n            // Try to parse key-value pairs (e.g., \"Power: 3 W\", \"Model: HW80\")\n            const colonIndex = item.indexOf(':');\n            if (colonIndex > 0 && colonIndex < item.length - 1) {\n                specs.push({\n                    key: item.substring(0, colonIndex).trim(),\n                    value: item.substring(colonIndex + 1).trim()\n                });\n                return;\n            }\n            // Try to extract power specifications (e.g., \"3 W\", \"1500 W\")\n            const powerMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*W/i);\n            if (powerMatch) {\n                specs.push({\n                    key: 'Power Consumption',\n                    value: powerMatch[0]\n                });\n                return;\n            }\n            // Try to extract model numbers (e.g., \"Model HW80\", \"HW80\")\n            const modelMatch = item.match(/(?:Model\\s+)?([A-Z]+\\d+[A-Z]*\\d*)/i);\n            if (modelMatch) {\n                specs.push({\n                    key: 'Model Number',\n                    value: modelMatch[1]\n                });\n                return;\n            }\n            // Try to extract capacity (e.g., \"8 kg\", \"10 L\")\n            const capacityMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|l|liters?|litres?)/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: \"\".concat(capacityMatch[1], \" \").concat(capacityMatch[2].toLowerCase())\n                });\n                return;\n            }\n            // Try to extract RPM (e.g., \"1400 RPM\")\n            const rpmMatch = item.match(/(\\d+)\\s*rpm/i);\n            if (rpmMatch) {\n                specs.push({\n                    key: 'Spin Speed',\n                    value: \"\".concat(rpmMatch[1], \" RPM\")\n                });\n                return;\n            }\n            // Try to extract dimensions\n            const dimensionMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*(cm|mm|m)/i);\n            if (dimensionMatch) {\n                specs.push({\n                    key: 'Dimensions',\n                    value: \"\".concat(dimensionMatch[1], \" x \").concat(dimensionMatch[2], \" x \").concat(dimensionMatch[3], \" \").concat(dimensionMatch[4])\n                });\n                return;\n            }\n            // Try to extract weight\n            const weightMatch = item.match(/(\\d+(?:\\.\\d+)?)\\s*(kg|g)/i);\n            if (weightMatch && !item.toLowerCase().includes('capacity')) {\n                specs.push({\n                    key: 'Weight',\n                    value: \"\".concat(weightMatch[1], \" \").concat(weightMatch[2])\n                });\n                return;\n            }\n            // If no specific pattern matches, treat as a general specification\n            if (item.trim()) {\n                specs.push({\n                    key: 'Feature',\n                    value: item.trim()\n                });\n            }\n        });\n        return specs;\n    };\n    const isGenericContent = (text)=>{\n        const genericPhrases = [\n            'high quality',\n            'reliable performance',\n            'durable construction',\n            'professional-grade performance and reliability',\n            'designed for optimal performance',\n            'excellent features and performance',\n            'quality product designed for reliable performance',\n            'high-quality construction ensures durability',\n            'reliable performance for consistent results',\n            'user-friendly design for easy operation',\n            'premium quality features',\n            'excellent value for money',\n            'designed for long-lasting performance'\n        ];\n        const lowerText = text.toLowerCase();\n        return genericPhrases.some((phrase)=>lowerText.includes(phrase));\n    };\n    const filterGenericItems = (items)=>{\n        return items.filter((item)=>!isGenericContent(item));\n    };\n    const extractSpecsFromProductName = (productName)=>{\n        const specs = [];\n        const name = productName.toLowerCase();\n        // Extract model number\n        const modelMatch = productName.match(/([A-Z]+\\d+[-\\w]*)/);\n        if (modelMatch) {\n            specs.push({\n                key: 'Model',\n                value: modelMatch[1]\n            });\n        }\n        // Extract dimensions\n        const dimensionMatch = productName.match(/(\\d+(?:\\.\\d+)?)\\s*(?:cm|mm|inch)/i);\n        if (dimensionMatch) {\n            specs.push({\n                key: 'Size',\n                value: dimensionMatch[0]\n            });\n        }\n        // Extract capacity for washing machines\n        if (name.includes('washing') || name.includes('washer')) {\n            const capacityMatch = productName.match(/(\\d+(?:\\.\\d+)?)\\s*kg/i);\n            if (capacityMatch) {\n                specs.push({\n                    key: 'Capacity',\n                    value: capacityMatch[0]\n                });\n            }\n        }\n        // Extract power ratings\n        const powerMatch = productName.match(/(\\d+)\\s*w/i);\n        if (powerMatch) {\n            specs.push({\n                key: 'Power',\n                value: \"\".concat(powerMatch[1], \" W\")\n            });\n        }\n        return specs;\n    };\n    const parseDescription = (desc)=>{\n        if (!desc || !desc.trim()) return null;\n        // Clean up the description and handle the actual format we receive\n        const cleanDesc = desc.trim();\n        // Extract overview (everything before the first section)\n        const overviewMatch = cleanDesc.match(RegExp(\"^(.*?)(?=\\\\s*(?:Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\\\s*•)\", \"s\"));\n        let overview = overviewMatch ? overviewMatch[1].trim() : '';\n        // Filter out generic overview content\n        if (isGenericContent(overview)) {\n            overview = ''; // Hide generic overviews\n        }\n        // Find all sections using regex that matches our actual format\n        const sectionRegex = /(Key Features|Features|Technical Specifications|Specifications|Benefits|Ideal For):\\s*((?:•[^•]*)*)/g;\n        const sections = [];\n        let match;\n        while((match = sectionRegex.exec(cleanDesc)) !== null){\n            const title = match[1].trim();\n            const content = match[2].trim();\n            // Extract bullet points\n            const bulletRegex = /•\\s*([^•]+)/g;\n            const items = [];\n            let bulletMatch;\n            while((bulletMatch = bulletRegex.exec(content)) !== null){\n                const item = bulletMatch[1].trim();\n                if (item) {\n                    items.push(item);\n                }\n            }\n            if (items.length > 0) {\n                // Filter out generic items\n                const filteredItems = filterGenericItems(items);\n                // Only include section if it has non-generic content\n                if (filteredItems.length > 0) {\n                    // Determine section type\n                    let sectionType = 'list';\n                    if (title.toLowerCase().includes('specification')) {\n                        sectionType = 'specifications';\n                    } else if (title.toLowerCase().includes('feature')) {\n                        sectionType = 'features';\n                    }\n                    sections.push({\n                        title,\n                        items: filteredItems,\n                        type: sectionType\n                    });\n                }\n            }\n        }\n        return {\n            overview,\n            sections\n        };\n    };\n    const getSectionIcon = (title)=>{\n        const iconClass = \"w-5 h-5\";\n        switch(title.toLowerCase()){\n            case 'key features':\n            case 'features':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-amber-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 16\n                }, undefined);\n            case 'technical specifications':\n            case 'specifications':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-blue-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 16\n                }, undefined);\n            case 'benefits':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-green-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 16\n                }, undefined);\n            case 'ideal for':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-purple-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"\".concat(iconClass, \" text-gray-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const renderSpecificationTable = (items)=>{\n        const specs = parseSpecifications(items);\n        if (specs.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic text-center py-4\",\n                children: \"No specifications available\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg overflow-hidden border border-gray-200 shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-3 sm:px-4 lg:px-6 py-2 sm:py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-sm sm:text-base\",\n                        children: \"Technical Specifications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"divide-y divide-gray-200\",\n                    children: specs.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row hover:bg-blue-50 transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-2/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-gray-50 sm:border-r border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-semibold text-gray-800 block\",\n                                        children: spec.key\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:w-3/5 px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 bg-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-gray-900 font-medium block\",\n                                        children: spec.value || '—'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 px-3 sm:px-4 lg:px-6 py-1 sm:py-2 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 text-center\",\n                        children: \"Specifications may vary by model and region\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderFeatureGrid = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Key Features & Highlights\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-3 lg:gap-4\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200 hover:from-amber-100 hover:to-orange-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 sm:w-3 sm:h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1 sm:mt-1.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 317,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderBenefitsList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3 sm:space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-green-600 to-emerald-600 px-3 sm:px-4 py-2 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-white font-semibold text-xs sm:text-sm\",\n                        children: \"Benefits & Advantages\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 sm:space-y-3\",\n                    children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 hover:from-green-100 hover:to-emerald-100 transition-all duration-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-green-600 mt-0.5 mr-2 sm:mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs sm:text-sm text-gray-800 font-medium leading-relaxed\",\n                                    children: item\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderDefaultList = (items)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start p-2 hover:bg-gray-50 rounded-md transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-700\",\n                            children: item\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 359,\n            columnNumber: 7\n        }, undefined);\n    };\n    const generateFallbackContent = ()=>{\n        const specs = extractSpecsFromProductName(productName);\n        // Add price if available\n        if (productPrice) {\n            specs.push({\n                key: 'Price',\n                value: \"₹\".concat(productPrice.toLocaleString())\n            });\n        }\n        // Add brand if available\n        if (productBrand) {\n            specs.push({\n                key: 'Brand',\n                value: productBrand\n            });\n        }\n        // Generate dynamic overview based on product name and brand\n        const generateDynamicOverview = ()=>{\n            const name = productName.toLowerCase();\n            let overview = '';\n            if (productBrand) {\n                overview = \"The \".concat(productBrand, \" \").concat(productName);\n            } else {\n                overview = \"The \".concat(productName);\n            }\n            // Add specific descriptions based on product type\n            if (name.includes('lamp') || name.includes('light')) {\n                overview += ' provides efficient lighting for your space.';\n            } else if (name.includes('camera') || name.includes('dashcam')) {\n                overview += ' offers reliable recording and monitoring capabilities.';\n            } else if (name.includes('pump')) {\n                overview += ' delivers dependable inflation performance.';\n            } else if (name.includes('hood') || name.includes('chimney')) {\n                overview += ' ensures effective kitchen ventilation.';\n            } else if (name.includes('hinge') || name.includes('hardware')) {\n                overview += ' provides smooth and reliable operation.';\n            } else {\n                overview += ' is designed to meet your specific requirements.';\n            }\n            return overview;\n        };\n        return {\n            overview: generateDynamicOverview(),\n            sections: specs.length > 0 ? [\n                {\n                    title: 'Specifications',\n                    items: specs.map((spec)=>\"\".concat(spec.key, \": \").concat(spec.value)),\n                    type: 'specifications'\n                }\n            ] : []\n        };\n    };\n    const parsedDesc = parseDescription(description);\n    // If no meaningful content found, generate fallback\n    const finalDesc = parsedDesc && parsedDesc.sections.length > 0 ? parsedDesc : generateFallbackContent();\n    if (!finalDesc || finalDesc.sections.length === 0 && !finalDesc.overview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"prose prose-sm max-w-none\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-700 leading-relaxed\",\n                    children: description || \"\".concat(productName, \" - Product information will be updated soon.\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 432,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n            lineNumber: 431,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"product-description space-y-4 sm:space-y-6\",\n        children: [\n            finalDesc.overview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overview-section bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 sm:p-6 border border-blue-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 sm:w-6 sm:h-6 text-blue-600 mt-1 mr-2 sm:mr-3 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-base sm:text-lg font-semibold text-gray-900 mb-2\",\n                                    children: \"Product Overview\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed text-sm sm:text-base\",\n                                    children: finalDesc.overview\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                lineNumber: 445,\n                columnNumber: 9\n            }, undefined),\n            finalDesc.sections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"description-section bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>toggleSection(index),\n                            className: \"w-full px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center min-w-0 flex-1\",\n                                    children: [\n                                        getSectionIcon(section.title),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 sm:ml-3 text-sm sm:text-lg font-semibold text-gray-900 truncate\",\n                                            children: section.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500 flex-shrink-0\",\n                                            children: [\n                                                \"(\",\n                                                section.items.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, undefined),\n                                expandedSections.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_ChevronDown_ChevronUp_Package_Settings_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, undefined),\n                        expandedSections.has(index) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6\",\n                            children: [\n                                section.type === 'specifications' && renderSpecificationTable(section.items),\n                                section.type === 'features' && renderFeatureGrid(section.items),\n                                section.title.toLowerCase().includes('benefit') && renderBenefitsList(section.items),\n                                section.type === 'list' && !section.title.toLowerCase().includes('benefit') && renderDefaultList(section.items)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, undefined))\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductDescription.tsx\",\n        lineNumber: 442,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductDescription, \"b/WJgSHj1mMZYb0GaMgxOeMj6ek=\");\n_c = ProductDescription;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDescription);\nvar _c;\n$RefreshReg$(_c, \"ProductDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductDescription.tsx\n"));

/***/ })

});