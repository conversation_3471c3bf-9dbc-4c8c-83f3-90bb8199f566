"use client";

import ProductDescription from "../../components/product/ProductDescription";

const TestDescriptionPage = () => {
  const testDescription = `Haier HW80-IM12929CS3 Washing Machine Product Overview: The Haier HW80-IM12929CS3 Washing Machine is a high-quality washing machines designed for professional-grade performance and reliability. Key Features: • High quality • Reliable performance • Durable construction Specifications: • Power: 3 W • Model: HW80 Benefits: • <PERSON><PERSON>'s global expertise in home appliances Ideal For: • Professional washing machines applications • Home and personal use • Commercial and industrial settings`;

  console.log("Test description:", testDescription);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Test Product Description Component</h1>
      
      <div className="border p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-4">Raw Description:</h2>
        <p className="text-sm text-gray-600 mb-6 whitespace-pre-wrap">{testDescription}</p>
        
        <h2 className="text-lg font-semibold mb-4">Parsed Description:</h2>
        <ProductDescription
          description={testDescription}
          productName="HW80-IM12929CS3 Washing Machine"
          productPrice={35100}
          productBrand="Haier"
        />
      </div>
      
      <div className="mt-8 border p-4 rounded-lg bg-gray-50">
        <h2 className="text-lg font-semibold mb-4">Debug Info:</h2>
        <p>Description length: {testDescription.length}</p>
        <p>Contains "Product Overview": {testDescription.includes("Product Overview") ? "Yes" : "No"}</p>
        <p>Contains "Key Features": {testDescription.includes("Key Features") ? "Yes" : "No"}</p>
      </div>
    </div>
  );
};

export default TestDescriptionPage;
